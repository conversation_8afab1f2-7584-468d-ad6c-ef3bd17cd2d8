%layout-navbar {
  color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
}

// Vertical nav scrolled sticky elevated nav
%default-layout-vertical-nav-scrolled-sticky-elevated-nav {
  background-color: rgb(var(--v-theme-surface));
  box-shadow: 0 4px 8px -4px rgb(94 86 105 / 42%);
}

// Floating navbar and sticky elevated navbar scrolled
%default-layout-vertical-nav-floating-navbar-and-sticky-elevated-navbar-scrolled {
  background-color: rgb(var(--v-theme-surface));
  box-shadow: 0 4px 8px -4px rgb(94 86 105 / 42%);
}

// Floating navbar overlay
%default-layout-vertical-nav-floating-navbar-overlay {
  backdrop-filter: blur(8px);
  background-color: rgba(var(--v-theme-surface), 0.9);
}
