// 公共样式 - 所有主题都需要
@tailwind base;
@tailwind components;
@tailwind utilities;

// 基础样式
html.v-overlay-scroll-blocked {
  position: fixed;
  position: relative;

  --v-body-scroll-y: 0px !important;
}

body {
  overscroll-behavior: none;
}

@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

@media (width <= 768px) {
  html,body {
    @include hide-scrollbar;
  }
}

// 进度条样式
#nprogress .bar {
  background: rgb(var(--v-theme-primary)) !important;
  inset-block-start: env(safe-area-inset-top) !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px rgb(var(--v-theme-primary)), 0 0 5px rgb(var(--v-theme-primary)) !important;
  inline-size: 5px;
  transform: rotate(0deg) translate(0, 0);
}

// 卡片高度匹配
.match-height.v-row {
  .v-card {
    block-size: 100%;
  }
}

// Toast通知样式
.Vue-Toastification__container {
  z-index: 2500;
  margin-block: env(safe-area-inset-top) env(safe-area-inset-bottom);
}

@media only screen and (width <= 600px){
  .Vue-Toastification__container {
    inline-size: 100vw;
    padding-block: 4.5rem;
    padding-inline: 1rem;
  }

  .Vue-Toastification__toast {
    border-radius: 8px;
  }
}

// 对话框样式
.v-dialog > .v-overlay__content > .v-card > .v-card-item {
  padding: 16px;
}

// 路由过渡动画
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.6s;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(-45px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(45px);
}

// 网格布局样式
.grid-info-card {
  grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
  padding-block-end: 1rem;
}

.grid-site-card {
  grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
  padding-block-end: 1rem;
}

.grid-media-card {
  grid-template-columns: repeat(auto-fill, minmax(9rem, 1fr));
}

.grid-backdrop-card {
  grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
}

.grid-torrent-card {
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
}

.grid-plugin-card {
  grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
}

.grid-downloading-card {
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
}

.grid-directory-card {
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
}

.grid-filterrule-card {
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
}

.grid-customrule-card {
  grid-template-columns: repeat(auto-fill, minmax(12rem, 1fr));
}

.grid-subscribe-card {
  grid-template-columns: repeat(auto-fill, minmax(15rem, 1fr));
}

.grid-user-card {
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
}

.grid-app-card {
  grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
}

.grid-workflow-card {
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
}

.grid-workflow-share-card {
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
}

// 文本样式
.text-moviepilot {
  background-clip: text;
  background-image: linear-gradient(to bottom right,var(--tw-gradient-stops));
  color: transparent;

  --tw-gradient-from: #818cf8;
  --tw-gradient-stops: var(--tw-gradient-from),var(--tw-gradient-to);
  --tw-gradient-to: #c084fc;
}

.text-shadow {
  text-shadow: 1px 1px #777;
}

// 滑块标题样式
.slider-header {
  position: relative;
  display: flex;
}

.slider-title {
  display: inline-flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.75rem;
}

@media (width >= 640px){
  .slider-title {
    overflow: hidden;
    font-size: 1.5rem;
    line-height: 2.25rem;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  block-size: 4px;
  inline-size: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background: rgb(var(--v-theme-perfect-scrollbar-thumb));
  box-shadow: inset 0 0 10px rgba(0,0,0,20%);

  @media(hover){
    &:hover{
      background: #a1a1a1;
    }
  }
}

*:hover::-webkit-scrollbar {
  opacity: 1;
}

*:active::-webkit-scrollbar {
  opacity: 1;
}

// 组件样式
.v-alert--variant-elevated, .v-alert--variant-flat {
  background: rgb(var(--v-table-header-background));
  color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
}

.backdrop-blur {
  --tw-backdrop-blur: blur(8px)!important;

  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)!important;
}

.v-toolbar{
  background: rgb(var(--v-table-header-background));
}

.v-divider {
  border-color: rgba(var(--v-theme-on-background), var(--v-selected-opacity));
  opacity:0.75;
}

.apexcharts-title-text {
  color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity)) !important;
}

.v-tabs:not(.v-tabs-pill).v-tabs--horizontal {
  border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.v-fab__container {
  padding-block-end: env(safe-area-inset-bottom);
}

.card-cover-blurred::before {
  position: absolute;
  backdrop-filter: blur(2px);
  background: rgba(29, 39, 59, 48%);
  content: '';
  inset: 0;
}

// 弹出层样式
.v-overlay__content .v-list{
  backdrop-filter: blur(6px);
  background-color: rgb(var(--v-theme-surface), 0.9) !important;
}

.v-overlay__content .v-card:not(.bg-primary){
  backdrop-filter: blur(8px);
  background-color: rgb(var(--v-theme-surface), 0.95) !important;

  .v-list, .v-table {
    backdrop-filter: none;
    background-color: transparent !important;
  }
}

.v-menu {
  .v-list-item:hover {
    background-color: rgba(var(--v-theme-on-surface), 0.04) !important;
  }
}

.v-btn.v-btn--icon {
  transition: opacity 0.15s ease;
}

.v-btn.v-btn--icon:hover {
  opacity: 0.85;
}

.v-overlay__content {
  margin-block: env(safe-area-inset-top) env(safe-area-inset-bottom);
  transition: opacity 0.2s ease !important;
}

.v-menu > .v-overlay__content {
  overflow: hidden;
}

.v-dialog--fullscreen > .v-overlay__content > .v-card {
  padding-block-end: calc(env(safe-area-inset-top) + env(safe-area-inset-bottom));
}

.v-dialog > .v-overlay__content {
  margin-block: env(safe-area-inset-top) env(safe-area-inset-bottom);
}

.v-bottom-sheet > .v-bottom-sheet__content.v-overlay__content > .v-card {
  padding-block-end: env(safe-area-inset-bottom);
}

.settings-icon-button {
  flex-shrink: 0;
  min-inline-size: auto;
}

.v-infinite-scroll__side {
  padding: 0;
}

.v-menu .v-overlay__content {
  box-shadow: none !important;
} 
