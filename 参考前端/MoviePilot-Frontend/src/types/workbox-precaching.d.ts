// Type definitions for workbox-precaching runtime use in service worker
// Simplified subset needed by this project

declare module 'workbox-precaching' {
  /**
   * A manifest entry generated by Workbox build tools.
   */
  export interface ManifestEntry {
    url: string
    revision?: string
  }

  /**
   * Removes outdated precaches created by older versions of Workbox.
   */
  export function cleanupOutdatedCaches(): void

  /**
   * Adds the supplied manifest entries to the precache list and sets up the
   * appropriate route so that they are served from the cache.
   */
  export function precacheAndRoute(entries: ManifestEntry[]): void
}