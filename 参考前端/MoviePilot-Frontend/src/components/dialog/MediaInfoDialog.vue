<script setup lang="ts">
import { Context } from '@/api/types'
import MediaInfoCard from '../cards/MediaInfoCard.vue'
import { useI18n } from 'vue-i18n'

// 多语言支持
const { t } = useI18n()

// 输入参数
defineProps({
  context: Object as PropType<Context>,
})

// 定义事件
const emit = defineEmits(['close'])
</script>
<template>
  <DialogWrapper max-width="50rem">
    <VCard>
      <VDialogCloseBtn @click="emit('close')" />
      <VCardItem>
        <MediaInfoCard :context="context" />
      </VCardItem>
    </VCard>
  </DialogWrapper>
</template>
