export default {
  common: {
    confirm: '確認',
    cancel: '取消',
    save: '儲存',
    close: '關閉',
    version: '版本',
    author: '作者',
    delete: '刪除',
    edit: '編輯',
    add: '添加',
    search: '搜索',
    loading: '加載中',
    success: '成功',
    error: '錯誤',
    openInNewWindow: '在新窗口中打開',
    inputMessage: '輸入消息或命令',
    send: '發送',
    noData: '暫無數據',
    noContent: '沒有找到相關內容',
    all: '全部',
    active: '激活',
    inactive: '未激活',
    filter: '篩選',
    noMatchingData: '沒有符合條件的數據',
    tryChangingFilters: '請嘗試更改篩選條件',
    default: '默認',
    name: '名稱',
    create: '新建',
    saving: '保存中',
    reset: '重置',
    theme: '主題',
    language: '語言',
    pleaseWait: '請稍候...',
    viewDetails: '查看詳情',
    user: '用戶',
    config: '配置',
    pause: '暫停',
    enable: '啟用',
    confirmAction: '確認{action}',
    details: '詳情',
    files: '文件',
    share: '分享',
    subscribe: '訂閱',
    unsubscribe: '取消訂閱',
    media: '媒體',
    unknown: '未知',
    notice: '注意',
    itemsPerPage: '每頁條數',
    pageText: '{0}-{1} 共 {2} 條',
    noDataText: '沒有數據',
    loadingText: '加載中...',
    networkRequired: '此功能需要網絡連接',
    networkDisconnected: '網絡連接已斷開',
    featuresLimited: '部分功能可能受限',
    serverConnectionFailed: '服務器連接失敗',
    troubleshooting: '疑難排解',
    checking: '檢查中',
    retry: '重試',
    networkOnline: '網絡在線',
    networkOffline: '網絡離線',
    serviceAvailable: '服務可用',
    serviceUnavailable: '服務不可用',
    status: '狀態',
    preset: '預設',
  },
  mediaType: {
    movie: '電影',
    tv: '電視劇',
    anime: '動漫',
    collection: '合集',
    unknown: '未知',
  },
  notificationSwitch: {
    resourceDownload: '資源下載',
    organize: '整理入庫',
    subscribe: '訂閱',
    site: '站點',
    mediaServer: '媒體伺服器',
    manual: '手動處理',
    plugin: '插件',
    other: '其它',
  },
  actionStep: {
    addDownload: '添加下載',
    addSubscribe: '添加訂閱',
    fetchDownloads: '獲取下載任務',
    fetchMedias: '獲取媒體數據',
    fetchRss: '獲取RSS資源',
    fetchTorrents: '獲取站點資源',
    filterMedias: '過濾媒體數據',
    filterTorrents: '過濾資源',
    scanFile: '掃描目錄',
    scrapeFile: '刮削文件',
    sendEvent: '發送事件',
    sendMessage: '發送消息',
    transferFile: '整理文件',
    invokePlugin: '調用插件',
    note: '備註',
  },
  qualityOptions: {
    all: '全部',
    blurayOriginal: '藍光原盤',
    remux: 'Remux',
    bluray: 'BluRay',
    uhd: 'UHD',
    webdl: 'WEB-DL',
    hdtv: 'HDTV',
    h265: 'H265',
    h264: 'H264',
  },
  resolutionOptions: {
    all: '全部',
    '4k': '4k',
    '1080p': '1080p',
    '720p': '720p',
  },
  effectOptions: {
    all: '全部',
    dolbyVision: '杜比視界',
    dolbyAtmos: '杜比全景聲',
    hdr: 'HDR',
    sdr: 'SDR',
  },
  theme: {
    light: '淺色',
    dark: '深色',
    auto: '跟隨系統',
    transparent: '透明',
    purple: '幻紫',
    custom: '附加樣式',
    transparency: '透明度',
    transparencyAdjust: '透明度調整',
    transparencyOpacity: '透明度',
    transparencyBlur: '模糊度',
    transparencyReset: '重置',
    transparencyLow: '低透明度',
    transparencyMedium: '中等透明度',
    transparencyHigh: '高透明度',
    customCssSaveSuccess: '自定義CSS保存成功，請刷新頁面生效！',
    customCssSaveFailed: '保存自定義CSS到服務端失敗',
    deviceNotSupport: '當前設備不支持監聽系統主題變化',
  },
  app: {
    moviepilot: 'MoviePilot',
    slogan: '智能影視媒體庫管理工具',
    recommend: '推薦',
    subscribeMovie: '電影訂閱',
    subscribeTv: '電視劇訂閱',
    settings: '設置',
    language: '語言設置',
    selectLanguage: '選擇語言',
    logout: '退出登錄',
    restarting: '正在重啟...',
    confirmRestart: '確認重啟系統嗎？',
    restartTip: '重啟後，您將被註銷並需要重新登錄。',
    restartTimeout: '重啟超時，系統可能需要更長時間恢復，請稍後手動刷新頁面',
    restartFailed: '重啟失敗，請檢查系統狀態',
    offline: '應用已離線',
    offlineMessage: '網絡連接已斷開，部分功能可能受限',
    online: '應用在線',
    onlineMessage: '網絡連接已恢復',
  },
  pwa: {
    installApp: '安裝 MoviePilot 應用',
    installDescription: '獲得更好的離線體驗和性能',
    install: '安裝',
    installSuccess: '應用安裝成功！',
    installGuide: '安裝指南',
    installInstructions: '在 {platform} 上安裝 MoviePilot：',
    installNote: '安裝後，您可以從主屏幕快速訪問 MoviePilot，並享受離線功能。',
    gotIt: '知道了',
    // 平台特定的說明
    platforms: {
      ios: 'iOS',
      android: 'Android',
      chrome: 'Chrome',
      edge: 'Edge',
      firefox: 'Firefox',
      safari: 'Safari',
      desktop: '桌面設備',
      mobile: '移動設備',
      other: '其他瀏覽器',
    },
    // 安裝步驟
    installSteps: {
      ios: {
        0: '點擊瀏覽器底部的分享按鈕',
        1: '選擇"添加到主屏幕"',
        2: '點擊"添加"確認安裝',
      },
      android: {
        0: '點擊瀏覽器菜單（三個點）',
        1: '選擇"添加到主屏幕"或"安裝應用"',
        2: '點擊"安裝"確認',
      },
      chrome: {
        0: '點擊地址欄右側的安裝圖標',
        1: '或者點擊瀏覽器菜單中的"安裝 MoviePilot"',
        2: '點擊"安裝"確認',
      },
      edge: {
        0: '點擊地址欄右側的應用圖標',
        1: '選擇"安裝此站點為應用"',
        2: '點擊"安裝"確認',
      },
      firefox: {
        0: '點擊地址欄右側的安裝圖標',
        1: '選擇"安裝"',
        2: '確認安裝到桌面',
      },
      safari: {
        0: '點擊分享按鈕',
        1: '選擇"添加到主屏幕"',
        2: '點擊"添加"確認',
      },
      desktop: {
        0: '點擊地址欄右側的安裝圖標',
        1: '選擇"安裝應用"',
        2: '按照提示完成安裝',
      },
      mobile: {
        0: '點擊瀏覽器菜單',
        1: '選擇"添加到主屏幕"',
        2: '確認安裝',
      },
      other: {
        0: '查找瀏覽器中的"安裝"選項',
        1: '通常在地址欄或菜單中',
        2: '按照提示完成安裝',
      },
    },
  },
  login: {
    wallpapers: '壁紙',
    username: '用戶名',
    password: '密碼',
    otpCode: '雙重驗證碼',
    stayLoggedIn: '保持登錄',
    login: '登錄',
    networkError: '登錄失敗，請檢查網絡連接！',
    authFailure: '登錄失敗，請檢查用戶名、密碼或雙重驗證是否正確！',
    permissionDenied: '登錄失敗，您沒有權限訪問！',
    serverError: '登錄失敗，服務器錯誤！',
    noPermission: '登錄失敗，您沒有任何功能權限，請聯繫管理員！',
    loginFailed: '登錄失敗',
    checkCredentials: '請檢查用戶名、密碼或雙重驗證碼是否正確！',
  },
  menu: {
    start: '開始',
    discovery: '發現',
    subscribe: '訂閱',
    organize: '整理',
    system: '系統',
  },
  navItems: {
    dashboard: '儀表盤',
    mediaInfo: '媒體庫',
    recommend: '推薦',
    site: '站點',
    search: '搜索',
    searchResult: '搜索結果',
    download: '下載',
    movieSubscribe: '電影訂閱',
    tvSubscribe: '電視劇訂閱',
    history: '歷史記錄',
    transfer: '整理',
    rename: '重命名',
    statistic: '統計',
    setting: '設置',
    plugin: '插件',
    user: '用戶',
    about: '關於',
    explore: '探索',
    movie: '電影',
    tv: '電視劇',
    workflow: '工作流',
    calendar: '日曆',
    downloadManager: '下載管理',
    mediaOrganize: '媒體整理',
    fileManager: '文件管理',
    pluginManager: '插件',
    siteManager: '站點管理',
    userManager: '用戶管理',
    settings: '設定',
  },
  settingTabs: {
    system: {
      title: '系統',
      description: '基礎設置、下載器（Qbittorrent、Transmission）、媒體服務器（Emby、Jellyfin、Plex）',
    },
    directory: {
      title: '存儲 & 目錄',
      description: '下載目錄、媒體庫目錄、整理、刮削',
    },
    site: {
      title: '站點',
      description: '站點同步、站點數據刷新、站點重置',
    },
    rule: {
      title: '規則',
      description: '自定義規則、優先級規則組、下載規則',
    },
    search: {
      title: '搜索 & 下載',
      description: '搜索數據源（TheMovieDb、豆瓣、Bangumi）、下載任務標籤、搜索站點',
    },
    subscribe: {
      title: '訂閱',
      description: '訂閱站點、訂閱模式、訂閱規則、洗版規則',
    },
    scheduler: {
      title: '服務',
      description: '定時作業',
    },
    cache: {
      title: '緩存',
      description: '種子緩存、識別媒體數據緩存、圖片文件緩存管理',
    },
    notification: {
      title: '通知',
      description: '通知渠道（微信、Telegram、Slack、SynologyChat、VoceChat、WebPush）、消息發送範圍',
    },
    words: {
      title: '詞表',
      description: '自定義識別詞、自定義製作組/字幕組、自定義占位符、文件整理屏蔽詞',
    },
    about: {
      title: '關於',
      description: '軟件版本',
    },
  },
  subscribeTabs: {
    movie: {
      mysub: '我的訂閱',
      popular: '熱門訂閱',
    },
    tv: {
      mysub: '我的訂閱',
      popular: '熱門訂閱',
      share: '訂閱分享',
    },
  },
  workflowTabs: {
    list: '我的工作流',
    share: '工作流分享',
  },
  pluginTabs: {
    installed: '我的插件',
    market: '插件市場',
  },
  discoverTabs: {
    themoviedb: 'TheMovieDb',
    douban: '豆瓣',
    bangumi: 'Bangumi',
  },
  user: {
    admin: '管理員',
    normal: '普通用戶',
    active: '激活',
    inactive: '已停用',
    noEmail: '未設置郵箱',
    movieSubscriptions: '電影訂閱',
    tvSubscriptions: '劇集訂閱',
    cannotDeleteCurrentUser: '不能刪除當前登入用戶！',
    confirmDeleteUser: '刪除用戶 {username} 的所有數據，是否確認？',
    deleteSuccess: '用戶刪除成功',
    deleteFailed: '用戶刪除失敗！',
    profile: '個人信息',
    systemSettings: '系統設定',
    siteAuth: '用戶認證',
    helpDocs: '幫助文檔',
    restart: '重啟',
    management: '用戶管理',
    noUsers: '沒有用戶',
    clickToAddUser: '點擊添加用戶卡片添加用戶',
    addUser: '添加用戶',
    editUser: '編輯用戶',
    username: '用戶名',
    password: '密碼',
    confirmPassword: '確認密碼',
    role: '角色',
    email: '郵箱',
    enabled: '啟用',
    disabled: '禁用',
    status: '狀態',
    operations: '操作',
  },
  nav: {
    more: '更多',
  },
  notification: {
    center: '通知中心',
    markRead: '設為已讀',
    empty: '暫無通知',
    channel: '通知渠道',
    name: '名稱',
    nameHint: '通知渠道名稱',
    type: '類型',
    typeHint: '通知渠道類型',
    customTypeHint: '自定義通知類型，用於插件實現場景',
    customTypePlaceholder: 'custom',
    nameRequired: '請輸入名稱',
    enabled: '啟用',
    config: '配置',
    wechat: {
      name: '企業微信',
      corpId: '企業ID',
      corpIdHint: '企業微信後台企業信息中的企業ID',
      appId: '應用 AgentId',
      appIdHint: '企業微信自建應用的AgentId',
      appSecret: '應用 Secret',
      appSecretHint: '企業微信自建應用的Secret',
      proxy: '代理地址',
      proxyHint: '微信消息的轉發代理地址，2022年6月20日後創建的自建應用才需要，不使用代理時需要保留默認值',
      token: 'Token',
      tokenHint: '微信企業自建應用->API接收消息配置中的Token',
      encodingAesKey: 'EncodingAESKey',
      encodingAesKeyHint: '微信企業自建應用->API接收消息配置中的EncodingAESKey',
      admins: '管理員白名單',
      adminsHint: '可使用管理菜單及命令的用戶ID列表，多個ID使用,分隔',
      adminsPlaceholder: '用戶ID列表，多個ID使用,分隔',
    },
    telegram: {
      name: 'Telegram',
      token: 'Bot Token',
      tokenHint: 'Telegram機器人token，格式：123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
      chatId: 'Chat ID',
      chatIdHint: '接受消息通知的用戶、群組或頻道Chat ID',
      users: '用戶白名單',
      usersHint: '可使用Telegram機器人的用戶ID清單，多個用戶用,分隔，不填寫則所有用戶都能使用',
      admins: '管理員白名單',
      adminsHint: '可使用管理菜單及命令的用戶ID列表，多個ID使用,分隔',
      adminsPlaceholder: '用戶ID列表，多個ID使用,分隔',
      usersPlaceholder: '用戶ID列表，多個ID使用,分隔',
    },
    slack: {
      name: 'Slack',
      oauthToken: 'Slack Bot User OAuth Token',
      oauthTokenHint: 'Slack應用`OAuth & Permissions`頁面中的`Bot User OAuth Token`',
      appToken: 'Slack App-Level Token',
      appTokenHint: 'Slack應用`OAuth & Permissions`頁面中的`App-Level Token`',
      channel: '頻道名稱',
      channelHint: '消息發送頻道，默認`全體`',
    },
    synologychat: {
      name: 'Synology Chat',
      webhook: '機器人傳入URL',
      webhookHint: 'Synology Chat機器人傳入URL',
      token: '令牌',
      tokenHint: 'Synology Chat機器人令牌',
    },
    vocechat: {
      name: 'VoceChat',
      host: '地址',
      hostHint: 'VoceChat服務端地址，格式：http(s)://ip:port',
      apiKey: '機器人密鑰',
      apiKeyHint: 'VoceChat機器人密鑰',
      channelId: '頻道ID',
      channelIdHint: 'VoceChat的頻道ID，不包含#號',
    },
    webpush: {
      name: 'WebPush',
      username: '登錄用戶名',
      usernameHint: '只有對應的用戶登錄後才會推送消息',
    },
  },
  shortcut: {
    title: '捷徑',
    recognition: {
      title: '識別',
      subtitle: '名稱識別測試',
    },
    rule: {
      title: '規則',
      subtitle: '規則測試',
    },
    log: {
      title: '日誌',
      subtitle: '實時日誌',
    },
    network: {
      title: '網絡',
      subtitle: '網速連通性測試',
    },
    system: {
      title: '系統',
      subtitle: '健康檢查',
    },
    message: {
      title: '消息',
      subtitle: '消息中心',
    },
  },
  workflow: {
    components: '動作組件',
    clickToAdd: '點擊添加',
    dragToCanvas: '拖曳至畫布',
    tapComponentHint: '點擊組件添加到畫布',
    dragComponentHint: '拖曳組件到畫布',
    task: {
      edit: '編輯任務',
      editFlow: '編輯流程',
      share: '分享',
      continue: '繼續',
      restart: '重新開始',
      run: '立即執行',
      reset: '重置任務',
      delete: '刪除任務',
      confirmDelete: '確定要刪除任務 {name} 嗎？',
      confirmReset: '確定要重置任務 {name} 嗎？',
      deleteSuccess: '任務刪除成功！',
      deleteFailed: '刪除任務失敗：{message}',
      enableSuccess: '任務啟用成功！',
      enableFailed: '啟用任務失敗：{message}',
      pauseSuccess: '任務暫停成功！',
      pauseFailed: '暫停任務失敗：{message}',
      runSuccess: '任務執行完成！',
      runFailed: '任務執行失敗：{message}',
      resetSuccess: '任務重置成功！',
      resetFailed: '重置任務失敗：{message}',
      status: {
        success: '成功',
        running: '執行中',
        failed: '失敗',
        paused: '已暫停',
        waiting: '等待中',
      },
      info: {
        trigger: '觸發方式',
        timer: '定時器',
        status: '狀態',
        actionCount: '動作數量',
        runCount: '執行次數',
        progress: '進度',
        error: '錯誤訊息',
        manualTrigger: '手動',
      },
    },
    scanFile: {
      title: '掃描目錄',
      subtitle: '掃描目錄文件到隊列',
      storage: '存儲',
      directory: '目錄',
    },
    addDownload: {
      title: '添加下载',
      subtitle: '添加资源到下载器',
      downloader: '下载器',
      category: '分类',
      savePath: '保存路径',
      sequential: '顺序下载',
      forceResume: '强制继续',
      firstLastPiece: '优先首尾文件',
      onlyLack: '仅下载缺失资源',
      categoryPlaceholder: '多個使用,分隔',
      savePathPlaceholder: '留空自動',
    },
    addSubscribe: {
      title: '添加订阅',
      subtitle: '添加资源到订阅',
      type: '类型',
      name: '名称',
      season: '季',
      episode: '集',
    },
    fetchMedias: {
      title: '獲取媒體數據',
      subtitle: '獲取榜單等媒體數據列表',
      source: '來源',
      searchType: '搜索方式',
      type: '類型',
      name: '名稱',
      year: '年份',
      ranking: '榜单',
      api: '插件API',
      apiPath: 'API路径',
      selectRanking: '选择榜单',
      tmdbTrending: '流行趨勢',
      doubanShowing: '正在熱映',
      bangumiCalendar: 'Bangumi每日放送',
      tmdbMovies: 'TMDB熱門電影',
      tmdbTvs: 'TMDB熱門電視劇',
      doubanMovieHot: '豆瓣熱門電影',
      doubanTvHot: '豆瓣熱門電視劇',
      doubanTvAnimation: '豆瓣熱門動漫',
      doubanMovies: '豆瓣最新電影',
      doubanTvs: '豆瓣最新電視劇',
      doubanMovieTop250: '豆瓣電影TOP250',
      doubanTvWeeklyChinese: '豆瓣國產劇集榜',
      doubanTvWeeklyGlobal: '豆瓣全球劇集榜',
    },
    filterMedias: {
      title: '過濾媒體數據',
      subtitle: '對媒體數據列表進行過濾',
      type: '類型',
      name: '名稱',
      year: '年份',
      vote: '評分',
    },
    scrapeFile: {
      title: '刮削文件',
      subtitle: '刮削媒體信息和圖片',
    },
    sendEvent: {
      title: '發送事件',
      subtitle: '發送任務執行事件',
    },
    fetchDownloads: {
      title: '獲取下載任務',
      subtitle: '獲取下載隊列中的任務狀態',
      loop: '循環執行',
      loopInterval: '循環間隔 (秒)',
    },
    fetchRss: {
      title: '獲取RSS資源',
      subtitle: '訂閱RSS地址獲取資源',
      url: 'RSS地址',
      userAgent: 'User-Agent',
      timeout: '超時時間',
      matchMedia: '匹配媒體信息',
      useProxy: '使用代理',
    },
    fetchTorrents: {
      title: '搜索站點資源',
      subtitle: '搜索站點種子資源列表',
      searchType: '搜索方式',
      searchOptions: {
        name: '名稱',
        mediaList: '媒體列表',
      },
      name: '名稱',
      year: '年份',
      type: '類型',
      season: '季',
      sites: '站點',
      matchMedia: '匹配媒體信息',
    },
    sendMessage: {
      title: '發送消息',
      subtitle: '發送任務執行消息',
      channel: '渠道',
      userId: '用戶ID',
    },
    transferFile: {
      title: '整理文件',
      subtitle: '整理重命名隊列中的文件',
      source: '來源',
      sourceOptions: {
        fileList: '文件列表',
        downloads: '下載任務',
      },
    },
    filterTorrents: {
      title: '過濾資源',
      subtitle: '對資源列表數據進行過濾',
      quality: '質量',
      resolution: '分辨率',
      effect: '特效',
      size: '大小範圍',
      include: '包含（關鍵字、正則式）',
      exclude: '排除（關鍵字、正則式）',
      ruleGroups: '過濾規則組',
    },
    invokePlugin: {
      title: '調用插件',
      subtitle: '調用插件執行特定操作',
      plugin: '插件',
      actionid: '動作ID',
      actionParams: '動作參數',
      loadPluginSettingFailed: '加載插件設置失敗',
    },
    note: {
      title: '備註',
      subtitle: '添加流程說明註釋',
      content: '備註內容',
      placeholder: '請輸入備註內容...',
    },
    title: '工作流',
    share: '工作流分享',
    searchShares: '搜索工作流分享',
    noShareData: '暫無分享的工作流',
    sharer: '分享人',
    trigger: '觸發方式',
    timer: '定時器',
    manualTrigger: '手動觸發',
    actionCount: '動作數量',
    normalFork: '復用工作流',
    cancelShare: '取消分享',
    cancelSuccess: '取消分享成功',
    cancelFailed: '取消分享失敗：{message}',
    usageCount: '復用 {count} 次',
    addSuccess: '復用 {name} 成功！',
    addFailed: '復用 {name} 失敗：{message}',
    noWorkflow: '沒有工作流',
    noWorkflowDescription: '點擊添加按鈕創建工作流任務。',
  },
  dashboard: {
    storage: '存儲空間',
    mediaStatistic: '媒體統計',
    weeklyOverview: '最近入庫',
    realTimeSpeed: '實時速率',
    scheduler: '後台任務',
    cpu: 'CPU',
    memory: '內存',
    network: '網絡流量',
    upload: '上行',
    download: '下行',
    library: '我的媒體庫',
    playing: '繼續觀看',
    latest: '最近添加',
    settings: '設置儀表板',
    chooseContent: '選擇您想在頁面顯示的內容',
    adaptiveHeight: '自適應組件高度',
    current: '當前',
    episodes: '劇集',
    users: '用戶',
    noSchedulers: '沒有後台服務',
    weeklyOverviewDescription: '最近一週入庫了 {count} 部影片',
    speed: {
      totalUpload: '總上傳量',
      totalDownload: '總下載量',
      freeSpace: '磁盤剩餘空間',
    },
    processes: {
      title: '系統進程',
      pid: '進程ID',
      name: '進程名稱',
      runtime: '運行時間',
      memory: '內存佔用',
    },
    errors: {
      loadMediaServer: '加載媒體服務器設置失敗:',
      loadLatest: '加載媒體服務器 "{server}" 的最近入庫失敗:',
    },
  },
  media: {
    status: {
      inLibrary: '已入庫',
      missing: '缺失',
      partiallyMissing: '部分缺失',
      subscribed: '已訂閱',
    },
    minutes: '分鐘',
    overview: '簡介',
    seasons: '季',
    seasonNumber: '第 {number} 季',
    episodeCount: '{count}集',
    actions: {
      searchResource: '搜索資源',
      subscribe: '訂閱',
      playOnline: '線上播放',
      playInApp: 'APP播放',
      playInWeb: '網頁播放',
    },
    search: {
      byTitle: '標題',
      byImdb: 'IMDB鏈接',
    },
    info: {
      originalTitle: '原始標題',
      status: '狀態',
      releaseDate: '上映日期',
      originalLanguage: '原始語言',
      productionCountries: '出品國家',
      productionCompanies: '製作公司',
      doubanId: '豆瓣ID',
    },
    subscribe: {
      normal: '訂閱',
      bestVersion: '洗版訂閱',
      addFailed: '添加訂閱失敗：{reason}！',
      canceled: '已取消訂閱！',
      cancelFailed: '取消訂閱失敗：{reason}！',
    },
    castAndCrew: '演員陣容',
    recommendations: '推薦',
    similar: '類似',
    error: {
      title: '出錯啦！',
      noMediaInfo: '未識別到媒體信息。',
    },
    server: {
      plex: 'Plex',
      jellyfin: 'Jellyfin',
      emby: 'Emby',
      appLaunchFailed: 'APP啟動失敗，正在跳轉到網頁版',
      appNotInstalled: '未檢測到APP，正在跳轉到網頁版',
      downloadApp: '下載APP',
    },
  },
  subscribe: {
    normalSub: '訂閱',
    versionSub: '洗版訂閱',
    addSuccess: '添加{name}成功！',
    addFailed: '添加{name}失敗：{message}！',
    cancelSuccess: '已取消訂閱！',
    cancelFailed: '取消訂閱失敗：{message}！',
    filterSubscriptions: '篩選訂閱',
    name: '名稱',
    searchShares: '搜索訂閱分享',
    keyword: '關鍵詞',
    noShareData: '未獲取到分享訂閱數據，未開啟數據分享或服務器無法連接。',
    noPopularData: '未獲取到熱門訂閱數據，未開啟數據分享或服務器無法連接。',
    noFilterData: '沒有篩選到相關內容，請更換篩選條件。',
    noSubscribeData: '請通過搜索添加電影、電視劇訂閱。',
    sharer: '分享人',
    follow: '關注',
    unfollow: '取消關注',
    recognitionWords: '識別詞',
    cancelShare: '取消分享',
    usageCount: '共 {count} 次複用',
    confirmToggle: '是否{action}訂閱 {name}？',
    toggleSuccess: '{name} 已{action}！',
    toggleFailed: '{action}失敗：{message}',
    resetConfirm: '重置後 {name} 將恢復初始狀態，已下載記錄將被清除，未入庫的內容將會重新下載，是否確認？',
    resetSuccess: '{name} 重置成功！',
    resetFailed: '{name} 重置失敗：{message}',
    shareStatistics: '分享統計',
    shareCount: '個分享',
    totalReuseCount: '次複用',
    ranking: '排名',
    noStatisticsData: '暫無分享統計數據',
    bestVersion: '洗版中',
    completed: '訂閱完成',
    subscribing: '訂閱中',
    notStarted: '未開始',
    pending: '待定',
    paused: '暫停',
  },
  recommend: {
    all: '全部',
    categoryMovie: '電影',
    categoryTV: '電視劇',
    categoryAnime: '動漫',
    categoryRankings: '榜單',
    trendingNow: '流行趨勢',
    nowShowing: '正在熱映',
    bangumiDaily: 'Bangumi每日放送',
    tmdbHotMovies: 'TMDB熱門電影',
    tmdbHotTVShows: 'TMDB熱門電視劇',
    doubanHotMovies: '豆瓣熱門電影',
    doubanHotTVShows: '豆瓣熱門電視劇',
    doubanHotAnime: '豆瓣熱門動漫',
    doubanNewMovies: '豆瓣最新電影',
    doubanNewTVShows: '豆瓣最新電視劇',
    doubanTop250: '豆瓣電影TOP250',
    doubanChineseTVRankings: '豆瓣國產劇集榜',
    doubanGlobalTVRankings: '豆瓣全球劇集榜',
    noCategoryContent: '當前分類下沒有可顯示的內容',
    configureContent: '設置顯示內容',
    customizeContent: '自定義內容',
    selectContentToDisplay: '選擇您想在頁面顯示的內容',
    selectAll: '全選',
    selectNone: '全不選',
  },
  discover: {
    setTabOrder: '設置標籤順序',
    dragToReorder: '拖動對標籤頁進行排序',
  },
  downloading: {
    noDownloader: '沒有下載器',
    configureDownloader: '請先在設置中正確配置並啟用下載器。',
    title: '下載',
    noTask: '沒有任務',
    noTaskDescription: '正在下載的任務將會顯示在這裡。',
  },
  resource: {
    searchResults: '資源搜索結果',
    keyword: '關鍵詞',
    title: '標題',
    year: '年份',
    season: '季',
    switchingView: '切換視圖',
    backToHome: '返回首頁',
    searching: '正在搜索，請稍候...',
    noData: '沒有數據',
    noResourceFound: '未搜索到任何資源',
  },
  browse: {
    actor: '演員',
  },
  appcenter: {
    others: '其他',
  },
  notFound: {
    title: '⚠️ 頁面不存在',
    description: '您想要訪問的頁面不存在，請檢查地址是否正確。',
    backButton: '返回',
  },
  torrent: {
    selectAll: '全選',
    clear: '清除',
    clearFilters: '清除篩選',
    confirm: '確定',
    resources: '個資源',
    noResults: '沒有找到匹配的資源',
    sortDefault: '默認',
    sortSite: '站點',
    sortSize: '大小',
    sortSeeder: '做種數',
    sortPublishTime: '發布時間',
    filterSite: '站點',
    filterSeason: '季',
    filterFreeState: '促銷狀態',
    filterVideoCode: '視頻編碼',
    filterEdition: '质量',
    filterResolution: '分辨率',
    filterReleaseGroup: '製作組',
    noMatchingResults: '沒有數據',
    allFilters: '綜合篩選',
    clearAll: '清除全部',
  },
  calendar: {
    episode: '第{number}集',
  },
  storage: {
    name: '名稱',
    type: '類型',
    customTypeHint: '自定義存儲類型，用於插件等場景',
    usedPercent: '已使用 {percent}%',
    noConfigNeeded: '此存儲類型無需配置參數，請直接配置目錄！',
    notConfigured: '未配置',
    local: '本地',
    alipan: '阿里雲盤',
    u115: '115網盤',
    rclone: 'RClone',
    alist: 'OpenList',
    smb: 'SMB網路共享',
    custom: '自定義',
  },

  filterRules: {
    specSub: '特效字幕',
    cnSub: '中文字幕',
    cnVoi: '國語配音',
    gz: '官種',
    notCnVoi: '排除: 國語配音',
    hkVoi: '粵語配音',
    notHkVoi: '排除: 粵語配音',
    free: '促銷: 免費',
    resolution4k: '解析度: 4K',
    resolution1080p: '解析度: 1080P',
    resolution720p: '解析度: 720P',
    not720p: '排除: 720P',
    qualityBlu: '品質: 藍光原盤',
    notBlu: '排除: 藍光原盤',
    qualityBluray: '品質: BLURAY',
    notBluray: '排除: BLURAY',
    qualityUhd: '品質: UHD',
    notUhd: '排除: UHD',
    qualityRemux: '品質: REMUX',
    notRemux: '排除: REMUX',
    qualityWebdl: '品質: WEB-DL',
    notWebdl: '排除: WEB-DL',
    quality60fps: '品質: 60fps',
    not60fps: '排除: 60fps',
    codecH265: '編碼: H265',
    notH265: '排除: H265',
    codecH264: '編碼: H264',
    notH264: '排除: H264',
    effectDolby: '效果: 杜比視界',
    notDolby: '排除: 杜比視界',
    effectAtmos: '效果: 杜比全景聲',
    notAtmos: '排除: 杜比全景聲',
    effectHdr: '效果: HDR',
    notHdr: '排除: HDR',
    effectSdr: '效果: SDR',
    notSdr: '排除: SDR',
    effect3d: '效果: 3D',
    not3d: '排除: 3D',
  },
  transferType: {
    copy: '複製',
    move: '移動',
    link: '硬連結',
    softlink: '軟連結',
  },
  site: {
    noSites: '沒有站點',
    noFilterData: '沒有符合條件的站點',
    sitesWillBeShownHere: '已添加並支持的站點將會在這裡顯示。',
    title: '站點',
    status: {
      enabled: '啟用',
      disabled: '停用',
    },
    fields: {
      url: '站點地址',
      priority: '優先級',
      status: '狀態',
      rss: 'RSS地址',
      timeout: '超時時間（秒）',
      downloader: '下載器',
      cookie: '站點Cookie',
      userAgent: '站點User-Agent',
      authorization: '請求頭（Authorization）',
      apiKey: '令牌（API Key）',
      limitAccess: '限制站點訪問頻率',
      limitInterval: '單位週期（秒）',
      limitCount: '週期內訪問次數',
      limitSeconds: '訪問間隔（秒）',
      useProxy: '使用代理訪問',
      browserSimulation: '瀏覽器仿真',
    },
    hints: {
      url: '格式：http://www.example.com/',
      priority: '優先級越小越優先',
      status: '站點啟用/停用',
      rss: '訂閱模式為`站點RSS`時使用的訂閱鏈接，如未自動獲取需手動補充',
      timeout: '站點請求超時時間，為0時不限制',
      downloader: '此站點使用的下載器',
      cookie: '站點請求頭中的Cookie信息',
      userAgent: '獲取Cookie的瀏覽器對應的User-Agent',
      authorization: '站點請求頭中的Authorization信息，特殊站點需要',
      apiKey: '站點的訪問API Key，特殊站點需要',
      limitInterval: '限流控制的單位週期時長',
      limitCount: '單位週期內允許的訪問次數',
      limitSeconds: '每次訪問需要間隔的最小時間',
      useProxy: '使用代理服務器訪問該站點',
      browserSimulation: '使用瀏覽器模擬真實訪問該站點',
    },
    actions: {
      add: '新增站點',
      edit: '編輯站點',
    },
    messages: {
      addSuccess: '新增站點成功',
      addFailed: '新增站點失敗',
      updateSuccess: '更新成功',
      updateFailed: '更新失敗',
    },
    errors: {
      loadDownloader: '加載下載器設置失敗',
    },
    testConnectivity: '測試連通性',
    testing: '測試中 ...',
    testSuccess: '{name} 連通性測試成功，可正常使用！',
    testFailed: '{name} 連通性測試失敗：{message}',
    connectionNormal: '連接正常',
    connectionSlow: '連接緩慢',
    connectionFailed: '連接失敗',
    connectionUnknown: '連接未知',
    deleteConfirm: '是否確認刪除站點？',
    deleteSuccess: '{name} 刪除成功！',
    deleteFailed: '{name} 刪除失敗：{message}',
    browseResources: '瀏覽資源',
    deleteSite: '刪除站點',
    updateCookie: '更新Cookie',
    viewUserData: '查看用戶數據',
    statistics: '統計信息',
    totalSites: '總站點數',
    normalSites: '正常站點',
    slowSites: '緩慢站點',
    failedSites: '失敗站點',
    averageTime: '平均耗時',
    successRate: '成功率',
    successCount: '成功次數',
    failCount: '失敗次數',
    lastAccess: '最後訪問',
    timeRecords: '耗時記錄',
    recentTimeRecords: '最近耗時記錄',
    accessTime: '訪問時間',
    responseTime: '響應時間',
    noTimeRecords: '暫無耗時記錄',
  },
  message: {
    loadMore: '加載更多',
    noMoreData: '沒有更多數據',
  },
  logging: {
    level: '級別',
    time: '時間',
    program: '程序',
    content: '內容',
    refreshing: '正在刷新',
  },
  moduleTest: {
    normal: '正常',
    disabled: '未啟用',
    error: '錯誤',
    checking: '正在檢查...',
    complete: '檢查完成',
    preparing: '準備檢查...',
    totalModules: '總模組數',
    recheck: '重新檢查',
  },
  nameTest: {
    recognize: '識別',
    recognizing: '識別中...',
    recognizeAgain: '重新識別',
    title: '標題',
    subtitle: '副標題',
  },
  netTest: {
    notTested: '未測試',
    testing: '測試中...',
    normal: '正常',
  },
  ruleTest: {
    test: '測試',
    testing: '正在測試...',
    testAgain: '重新測試',
    title: '標題',
    subtitle: '副標題',
    ruleGroup: '規則組',
    priority: '優先級：{value}',
    noPriorityRule: '未命中任何優先級規則！',
  },
  setting: {
    about: {
      title: '關於 MoviePilot',
      softwareVersion: '軟件版本',
      frontendVersion: '前端版本',
      authVersion: '認證資源版本',
      indexerVersion: '站點資源版本',
      configDir: '配置目錄',
      dataDir: '數據目錄',
      timezone: '時區',
      latest: '最新',
      support: '支援',
      supportingSites: '支持站點',
      documentation: '文檔',
      feedback: '問題反饋',
      channel: '發布頻道',
      versions: '軟件版本',
      latestVersion: '最新軟件版本',
      currentVersion: '當前版本',
      viewChangelog: '查看變更日誌',
      changelog: '變更日誌',
      dataDirectory: '/moviepilot',
      expand: '展開',
      collapse: '收起',
    },
    system: {
      custom: '自定義',
      basicSettings: '基礎設置',
      basicSettingsDesc: '設置服務器的全局功能',
      appDomain: '訪問域名',
      appDomainHint: '用於發送通知時，添加快捷跳轉地址',
      wallpaper: '背景壁紙',
      wallpaperHint: '選擇登陸頁面背景來源',
      recognizeSource: '識別數據源',
      recognizeSourceHint: '設置默認媒體信息識別數據源',
      mediaServerSyncInterval: '媒體服務器同步間隔',
      mediaServerSyncIntervalHint: '定時同步媒體服務器數據到本地的時間間隔',
      hours: '小時',
      required: '必選項，請勿留空',
      numbersOnly: '僅支持輸入數字，請勿輸入其他字符',
      minInterval: '間隔不能小於1個小時',
      apiToken: 'API令牌',
      apiTokenHint: '設置外部請求MoviePilot API時使用的token值',
      apiTokenMinChars: '不能小於16位字符',
      apiTokenRequired: '必填項；請輸入API Token',
      apiTokenLength: 'API Token不得低於16位',
      githubToken: 'Github Token',
      githubTokenFormat: 'ghp_**** 或 github_pat_****',
      githubTokenHint: '用於提高插件等訪問Github API時的限流閾值',
      ocrHost: '驗證碼識別服務器',
      ocrHostHint: '用於站點簽到、更新站點Cookie等識別驗證碼',
      advancedSettings: '高級設置',
      advancedSettingsDesc: '系統進階設置，特殊情況下才需要調整',
      downloaders: '下載器',
      downloadersDesc: '只有默認下載器才會被默認使用。',
      mediaServers: '媒體服務器',
      mediaServersDesc: '所有啟用的媒體服務器都會被使用。',
      trimeMedia: '飛牛影視',
      system: '系統',
      media: '媒體',
      network: '網絡',
      log: '日誌',
      lab: '實驗室',
      downloaderSaveSuccess: '下載器設置保存成功',
      downloaderSaveFailed: '下載器設置保存失敗！',
      defaultDownloaderNotice: '未設置默認下載器，已將【{name}】作為默認下載器',
      mediaServerSaveSuccess: '媒體服務器設置保存成功',
      mediaServerSaveFailed: '媒體服務器設置保存失敗！',
      saveFailed: '設置保存失敗：{message}！',
      basicSaveSuccess: '基礎設置保存成功',
      advancedSaveSuccess: '高級設置保存成功',
      copySuccess: '已複製到剪貼板！',
      copyFailed: '複製失敗：可能是瀏覽器不支持或被用戶阻止！',
      copyError: '複製失敗！',
      reloading: '正在應用配置...',
      qbittorrent: 'Qbittorrent',
      transmission: 'Transmission',
      emby: 'Emby',
      jellyfin: 'Jellyfin',
      plex: 'Plex',
      reloadSuccess: '系統配置已生效',
      reloadFailed: '重載系統失敗！',
      auxAuthEnable: '用戶輔助認證',
      auxAuthEnableHint: '允許外部服務進行登錄認證以及自動創建用戶',
      globalImageCache: '全局圖片緩存',
      globalImageCacheHint: '將媒體圖片緩存到本地，提升圖片加載速度',
      subscribeStatisticShare: '分享訂閱數據',
      subscribeStatisticShareHint: '分享訂閱統計數據到熱門訂閱，供其他MPer參考',
      pluginStatisticShare: '上報插件安裝數據',
      pluginStatisticShareHint: '上報插件安裝數據給服務器，用於統計展示插件安裝情況',
      workflowStatisticShare: '分享工作流數據',
      workflowStatisticShareHint: '分享工作流統計數據到熱門工作流，供其他MPer參考',
      bigMemoryMode: '大內存模式',
      bigMemoryModeHint: '使用更大的內存緩存數據，提升系統性能',
      dbWalEnable: 'WAL模式',
      dbWalEnableHint: '可提升讀寫併發性能，但可能在異常情況下增加數據丟失風險，更改後需重啟生效',
      tmdbApiDomain: 'TMDB API服務地址',
      tmdbApiDomainPlaceholder: 'api.themoviedb.org',
      tmdbApiDomainHint: '自定義themoviedb API域名或代理地址',
      tmdbApiDomainRequired: '請輸入TMDB API域名',
      tmdbImageDomain: 'TMDB 圖片服務地址',
      tmdbImageDomainPlaceholder: 'image.tmdb.org',
      tmdbImageDomainHint: '自定義themoviedb圖片服務域名或代理地址',
      tmdbImageDomainRequired: '請輸入圖片服務域名',
      tmdbLocale: 'TMDB 元數據語言',
      tmdbLocalePlaceholder: 'zh',
      tmdbLocaleHint: '自定義themoviedb元數據語言',
      metaCacheExpire: '媒體元數據緩存過期時間',
      metaCacheExpireHint: '識別元數據本地緩存時間，為 0 時使用內置默認值',
      metaCacheExpireRequired: '請輸入元數據緩存時間',
      metaCacheExpireMin: '元數據緩存時間必須大於等於0',
      scrapFollowTmdb: '跟隨TMDB識別整理',
      scrapFollowTmdbHint: '關閉時以整理歷史記錄為準（如有），避免TMDB數據在訂閱中途修改',
      scrapOriginalImage: 'TMDB 刮削原語种圖片',
      scrapOriginalImageHint: '刮削原語种圖片，否则數據元数据語种圖片',
      fanartEnable: 'Fanart圖片數據源',
      fanartEnableHint: '使用 fanart.tv 的圖片數據',
      fanartLang: 'Fanart語言',
      fanartLangHint: '設定Fanart圖片的語言偏好，多選時按優先級順序排列',
      githubProxy: 'Github加速代理',
      githubProxyPlaceholder: '留空表示不使用代理',
      githubProxyHint: '使用代理加速Github訪問速度',
      pipProxy: 'PIP加速代理',
      pipProxyPlaceholder: '留空表示不使用代理',
      pipProxyHint: '使用代理加速插件等pip庫安裝速度',
      dohEnable: 'DNS Over HTTPS',
      dohEnableHint: '使用DOH對特定域名進行解析，以防止DNS污染',
      dohResolvers: 'DOH 服務器',
      dohResolversPlaceholder: 'https://dns.google/dns-query,*******',
      dohResolversHint: 'DNS解析服務器地址，多個地址使用逗號分隔',
      dohDomains: 'DOH 域名',
      dohDomainsPlaceholder: 'example.com,example2.com',
      dohDomainsHint: '使用DOH解析的域名，多個域名使用逗號分隔',
      debug: '調試模式',
      debugHint: '啟用調試模式後，日誌將以DEBUG級別記錄，以便排查問題',
      logLevel: '日誌等級',
      logLevelHint: '設置日誌記錄的級別，用於控制日誌輸出量',
      logMaxFileSize: '日誌文件最大容量(MB)',
      logMaxFileSizeHint: '限制單個日誌文件的最大容量，超出後將自動分割日誌',
      logMaxFileSizeRequired: '日誌文件最大大小',
      logMaxFileSizeMin: '日誌文件最大容量必須大於等於1',
      logBackupCount: '日誌文件最大備份數量',
      logBackupCountHint: '設置每個模塊日誌文件的最大備份數量，超過後將覆蓋舊日誌',
      logBackupCountRequired: '請輸入日誌文件最大備份數量',
      logBackupCountMin: '日誌文件最大備份數量必須大於等於1',
      logFileFormat: '日誌文件格式',
      logFileFormatHint: '設置日誌文件的輸出格式，用於自定義日誌的顯示內容',
      pluginAutoReload: '插件熱加載',
      pluginAutoReloadHint: '修改插件文件後自動重新加載，開發插件時使用',
      encodingDetectionPerformanceMode: '編碼探測性能模式',
      encodingDetectionPerformanceModeHint: '優先提升探測效率，但可能降低編碼探測的準確性',
      tokenizedSearch: '分詞搜索',
      tokenizedSearchHint: '提升整理歷史記錄搜索精度，但可能增加性能開銷和意外結果',
      tmdbLanguage: {
        zhCN: '簡體中文',
        zhTW: '繁體中文',
        en: '英文',
      },
      fanartLanguage: {
        zh: '中文',
        en: '英文',
        ja: '日文',
        ko: '韓文',
        de: '德文',
        fr: '法文',
        es: '西班牙文',
        it: '意大利文',
        pt: '葡萄牙文',
        ru: '俄文',
      },
      logLevelItems: {
        debug: 'DEBUG - 調試',
        info: 'INFO - 信息',
        warning: 'WARNING - 警告',
        error: 'ERROR - 錯誤',
        critical: 'CRITICAL - 嚴重',
      },
      wallpaperItems: {
        tmdb: 'TMDB電影海報',
        bing: 'Bing每日壁紙',
        mediaserver: '媒體服務器',
        none: '無壁紙',
        customize: '自定義',
      },
      mb: 'MB',
      hour: '小時',
      customizeWallpaperApi: '自定義壁紙API',
      customizeWallpaperApiHint: '會獲取 API 返回內容中所有安全設置中允許的圖片地址，需要設置安全域名白名單',
      customizeWallpaperApiRequired: '必填項；請輸出自定義壁紙API',
      securityImageDomains: '安全圖片域名',
      securityImageDomainsHint: '允許緩存的圖片域名白名單，用於控制可信任的圖片來源',
      noSecurityImageDomains: '暫無安全域名',
      securityImageDomainAdd: '添加域名，如：image.tmdb.org',
      proxyHost: '代理服務器',
      proxyHostHint: '設置代理服務器地址，支持：http(s)、socks5、socks5h 等協議',
      moviePilotAutoUpdate: '自動更新MoviePilot',
      moviePilotAutoUpdateHint: '重啟時自動更新MoviePilot到最新發行版本',
      autoUpdateResource: '自動更新站點資源',
      autoUpdateResourceHint: '重啟時自動檢測和更新站點資源包',
      // 刮削開關設定
      scrapingSwitchSettings: '刮削開關設定',
      scrapingSwitchSettingsDesc: '控制各類媒體檔案的刮削功能開關',
      movie: '電影',
      tv: '電視劇',
      season: '季',
      episode: '集',
      movieNfo: 'NFO',
      moviePoster: '海報',
      movieBackdrop: '背景圖',
      movieLogo: 'Logo',
      movieDisc: '光碟圖',
      movieBanner: '橫幅圖',
      movieThumb: '縮略圖',
      tvNfo: 'NFO',
      seasonNfo: 'NFO',
      tvPoster: '海報',
      tvBackdrop: '背景圖',
      tvBanner: '橫幅圖',
      tvLogo: 'Logo',
      tvThumb: '縮略圖',
      seasonPoster: '海報',
      seasonBanner: '橫幅圖',
      seasonThumb: '縮略圖',
      episodeNfo: 'NFO',
      episodeThumb: '縮略圖',
      scrapingSwitchSaveFailed: '刮削開關設定保存失敗：{message}',
      scrapingSwitchSaveError: '刮削開關設定保存失敗',
    },
    site: {
      siteSync: '站點同步',
      siteSyncDesc: '從CookieCloud快速同步站點數據',
      enableLocalCookieCloud: '啟用本地CookieCloud服務器',
      enableLocalCookieCloudHint: '使用內建CookieCloud服務同步站點數據，服務地址為：http://localhost:3000/cookiecloud',
      serviceAddress: '服務地址',
      serviceAddressPlaceholder: 'https://movie-pilot.org/cookiecloud',
      serviceAddressHint: '遠端CookieCloud服務地址，格式：https://movie-pilot.org/cookiecloud',
      userKey: '用戶KEY',
      userKeyHint: 'CookieCloud瀏覽器插件生成的用戶KEY',
      e2ePassword: '端對端加密密碼',
      e2ePasswordHint: 'CookieCloud瀏覽器插件生成的端對端加密密碼',
      autoSyncInterval: '自動同步間隔',
      autoSyncIntervalHint: '從CookieCloud服務器自動同步站點Cookie到MoviePilot的時間間隔',
      syncBlacklist: '同步域名黑名單',
      syncBlacklistPlaceholder: '多個域名,分割',
      syncBlacklistHint: 'CookieCloud同步域名黑名單，多個域名,分割',
      userAgent: '瀏覽器User-Agent',
      userAgentHint: 'CookieCloud插件所在的瀏覽器的User-Agent',
      siteDataRefresh: '站點數據刷新',
      siteDataRefreshInterval: '站點數據刷新間隔',
      siteDataRefreshIntervalHint: '刷新站點用戶上傳下載等數據的時間間隔',
      readSiteMessage: '閱讀站點消息',
      readSiteMessageHint: '刷新數據時讀取站點消息並發送通知',
      siteReset: '站點重置',
      confirmReset: '確認刪除所有站點數據並重新同步。',
      confirmResetHint: '刪除所有站點數據並重新從CookieCloud同步，操作請先清空涉及站點的相關設置。',
      resetSites: '重置站點數據',
      resettingSites: '正在重置...',
      syncInterval: {
        hourly: '每小時',
        every6Hours: '每6小時',
        every12Hours: '每12小時',
        daily: '每天',
        weekly: '每週',
        monthly: '每月',
        never: '永不',
      },
      saveSuccess: '保存站點設置成功',
      saveFailed: '站點設置保存失敗！',
      resetSuccess: '站點重置成功，請等待CookieCloud同步完成！',
      resetFailed: '站點重置失敗！',
    },
    notification: {
      channels: '通知渠道',
      channelsDesc: '設置消息發送渠道參數',
      organizeSuccess: '資源入庫',
      downloadAdded: '資源下載',
      subscribeAdded: '添加訂閱',
      subscribeComplete: '訂閱完成',
      templateConfigTitle: '通知模板',
      templateConfigDesc: '設置通知模板，支持Jinja2語法。',
      templateSaveFailed: '模板保存失敗！',
      templateSaveSuccess: '模板保存成功',
      templateLoadFailed: '模板載入失敗！',
      scope: '通知發送範圍',
      scopeDesc: '對應消息類型只會發送給設定的用戶。',
      messageType: '消息類型',
      scopeRange: '範圍',
      operationUserOnly: '僅操作用戶',
      adminOnly: '僅管理員',
      userAndAdmin: '操作用戶和管理員',
      allUsers: '所有用戶',
      sendTime: '通知發送時間',
      sendTimeDesc: '設定消息發送的時間範圍。',
      startTime: '開始時間',
      endTime: '結束時間',
      saveSuccess: '通知設置保存成功',
      saveFailed: '通知設置保存失敗！',
      switchSaveSuccess: '消息類型開關保存成功',
      switchSaveFailed: '消息類型開關保存失敗！',
      timeSaveSuccess: '通知發送時間保存成功',
      timeSaveFailed: '通知發送時間保存失敗！',
      channel: '通知',
      wechat: '微信',
      resourceDownload: '資源下載',
      mediaImport: '整理入庫',
      subscription: '訂閱',
      site: '站點',
      mediaServer: '媒體服務器',
      manualProcess: '手動處理',
      plugin: '插件',
      other: '其它',
      telegram: 'Telegram',
      slack: 'Slack',
      synologyChat: 'SynologyChat',
      voceChat: 'VoceChat',
      webPush: 'WebPush',
      custom: '自定義通知',
    },
    words: {
      customIdentifiers: '自定義識別詞',
      identifiersDesc: '添加規則對種子名或者文件名進行預處理以校正識別',
      identifiersPlaceholder: '支持正則表達式，特殊字符需要\\轉義，一行為一組',
      identifiersHint: '支持正則表達式，特殊字符需要\\轉義，一行為一組',
      formatTitle: '支持的配置格式（注意空格）：',
      formatContent:
        '屏蔽詞\n' +
        '被替換詞 => 替換詞\n' +
        '前定位詞 <> 後定位詞 >> 集偏移量（EP）\n' +
        '被替換詞 => 替換詞 && 前定位詞 <> 後定位詞 >> 集偏移量（EP）\n' +
        '其中替換詞支持格式：&#123;[tmdbid/doubanid=xxx;type=movie/tv;s=xxx;e=xxx]&#125; 直接指定TMDBID/豆瓣ID識別，其中s、e為季數和集數（可選）',
      identifierSaveSuccess: '自定義識別詞保存成功',
      identifierSaveFailed: '自定義識別詞保存失敗！',

      customReleaseGroups: '自定義製作組/字幕組',
      releaseGroupsDesc: '添加無法識別的製作組/字幕組。',
      releaseGroupsPlaceholder: '支持正則表達式，特殊字符需要\\轉義，一行代表一個製作組/字幕組',
      releaseGroupsHint: '支持正則表達式，特殊字符需要\\轉義，一行代表一個製作組/字幕組',
      releaseGroupSaveSuccess: '自定義製作組/字幕組保存成功',
      releaseGroupSaveFailed: '自定義製作組/字幕組保存失敗！',

      customization: '自定義占位符',
      customizationDesc: '添加自定義占位符識別正則，重命名格式中添加{customization}使用。',
      customizationPlaceholder: '支持正則表達式，特殊字符需要\\轉義，多個匹配對象請換行分隔',
      customizationHint: '支持正則表達式，特殊字符需要\\轉義，多個匹配對象請換行分隔',
      customizationSaveSuccess: '自定義占位符保存成功',
      customizationSaveFailed: '自定義占位符保存失敗！',

      transferExcludeWords: '文件整理屏蔽詞',
      excludeWordsDesc: '目錄名或文件名中包含屏蔽詞時不進行整理。',
      excludeWordsPlaceholder: '支持正則表達式，特殊字符需要\\轉義，一行代表一個屏蔽詞',
      excludeWordsHint: '支持正則表達式，特殊字符需要\\轉義，一行代表一個屏蔽詞',
      excludeWordsSaveSuccess: '文件整理屏蔽詞保存成功',
      excludeWordsSaveFailed: '文件整理屏蔽詞保存失敗！',
    },
    search: {
      basicSettings: '基礎設置',
      basicSettingsDesc: '設定數據源、規則組等基礎信息',
      recognizeSource: '識別數據源',
      recognizeSourceDesc: '默認使用TMDB。豆瓣識別中文作品通常更友好，但有些國外作品信息不完整。',
      themoviedb: 'TheMovieDb',
      douban: '豆瓣',
      filterRuleGroup: '過濾規則組',
      filterRuleGroupDesc: '設置下載過程中使用的過濾規則組。',
      downloadLabel: '下載任務標籤',
      downloadLabelDesc: '下載器中的下載標籤，用於過濾查詢。',
      downloadLabelHint: '支持增加多個標籤，英文逗號分隔',
      downloadSite: '搜索站點',
      downloadSiteDesc: '設置指定分類搜索的站點範圍。',
      movieSites: '電影站點',
      tvSites: '電視劇站點',
      animeSites: '動漫站點',
      saveSites: '保存站點',
      saveSuccess: '保存搜索設置成功',
      saveFailed: '搜索設置保存失敗！',
      saveRuleFailed: '規則保存失敗！',
      movieCategory: '電影',
      tvCategory: '電視劇',
      animeCategory: '動漫',
      downloadUser: '遠程搜索自動下載用戶',
      multipleNameSearch: '多名稱資源搜索',
      multipleNameSearchHint: '使用多個名稱（中文、英文等）搜索站點資源並合並搜索結果，會增加站點訪問頻率',
      downloadSubtitle: '下載站點字幕',
      downloadSubtitleHint: '檢查站點資源是否有單獨的字幕文件並自動下載',
      mediaSource: '媒體搜索數據源',
      mediaSourceHint: '搜索媒體信息時使用的數據源以及排序',
      filterRuleGroupHint: '搜索媒體信息時按選定的過濾規則組對結果進行過濾',
      downloadUserPlaceholder: '用戶ID1,用戶ID2',
      downloadUserHint: '使用Telegram、微信等搜索時是否自動下載，使用逗號分割，設置為 all 代表所有用戶自動擇優下載',
      downloadLabelPlaceholder: 'MOVIEPILOT',
    },
    directory: {
      storage: '存儲',
      storageDesc: '設置本地或網盤存儲',
      directory: '目錄',
      directoryDesc: '設置媒體文件整理目錄結構，按先後順序依次匹配。',
      organizeAndScrap: '整理 & 刮削',
      organizeAndScrapDesc: '設置重命名格式、刮削選項等。',
      scrapSource: '刮削數據源',
      scrapSourceHint: '刮削時的元數據來源',
      movieRenameFormat: '電影重命名格式',
      movieRenameFormatHint: '使用Jinja2語法，格式參考：https://jinja.palletsprojects.com/en/3.0.x/templates',
      tvRenameFormat: '電視劇重命名格式',
      tvRenameFormatHint: '使用Jinja2語法，格式參考：https://jinja.palletsprojects.com/en/3.0.x/templates',
      saveSuccess: '存儲設置保存成功',
      saveFailed: '存儲設置保存失敗！',
      directorySaveSuccess: '目錄設置保存成功',
      directorySaveFailed: '目錄設置保存失敗！',
      organizeSaveSuccess: '整理選項設置保存成功',
      organizeSaveFailed: '整理選項設置保存失敗！',
      duplicateDirectoryName: '存在重複目錄名稱！無法保存，請修改！',
      defaultDirName: '目錄',
      storageSaveSuccess: '存儲設置保存成功',
      storageSaveFailed: '存儲設置保存失敗！',
    },
    rule: {
      customRules: '自定義規則',
      customRulesDesc: '自定義優先級規則項',
      priorityRuleGroups: '優先級規則組',
      priorityRuleGroupsDesc: '預設優先級規則組，以便在搜索和訂閱中使用。',
      downloadRules: '下載規則',
      downloadRulesDesc: '同時命中多個資源時擇優下載。',
      resourcePriority: '資源優先級',
      sitePriority: '站點優先級',
      siteUpload: '站點上傳量',
      resourceSeeder: '資源做種數',
      emptyIdError: '存在空ID的規則，無法保存，請修改！',
      emptyNameError: '存在空名字的規則，無法保存，請修改！',
      duplicateIdError: '存在重複規則ID！無法保存，請修改！',
      duplicateNameError: '存在重複規則名稱！無法保存，請修改！',
      customRuleSaveSuccess: '自定義規則保存成功',
      customRuleSaveFailed: '自定義規則保存失敗！',
      emptyGroupNameError: '存在空名字的規則組！無法保存，請修改！',
      duplicateGroupNameError: '存在重複規則組名稱！無法保存，請修改！',
      ruleGroupSaveSuccess: '優先級規則組保存成功',
      ruleGroupSaveFailed: '優先級規則組保存失敗！',
      customRuleCopySuccess: '自定義規則已複製到剪貼板！',
      customRuleCopyFailed: '自定義規則複製失敗：可能是瀏覽器不支持或被用戶阻止！',
      customRuleCopyError: '自定義規則複製失敗！',
      ruleGroupCopySuccess: '優先級規則組已複製到剪貼板！',
      ruleGroupCopyFailed: '優先級規則組複製失敗：可能是瀏覽器不支持或被用戶阻止！',
      ruleGroupCopyError: '優先級規則組複製失敗！',
      currentPriorityRules: '當前使用下載優先規則',
      currentPriorityRulesHint: '排在前面的優先級越高，未選擇的項不納入排序',
      importCustomRules: '導入自定義規則',
      importRuleGroups: '導入優先級規則組',
      importFailed: '導入規則失敗！無法解析輸入的數據！',
      importUnknownType: '導入規則失敗！未知的數據類型！',
      duplicateValue: '存在重名值',
      importNoId: '導入失敗！發現有規則不存在ID，可能屬於優先級規則組！',
      importHasId: '導入失敗！發現有規則存在相同ID，可能屬於自定義規則！',
    },
    scheduler: {
      scheduledTasks: '定時作業',
      scheduledTasksDesc: '包含系統內置服務以及插件提供的服務',
      provider: '提供者',
      taskName: '任務名稱',
      taskStatus: '任務狀態',
      nextRunTime: '下一次執行時間',
      execute: '執行',
      noService: '沒有後台服務',
      running: '正在運行',
      stopped: '已停止',
      waiting: '等待',
      executeSuccess: '定時作業執行請求提交成功！',
    },
    subscribe: {
      basicSettings: '基礎設置',
      basicSettingsDesc: '設定訂閱模式、週期等基礎設置',
      subscribeSites: '訂閱站點',
      subscribeSitesDesc: '只有選中的站點才會在訂閱中使用。',
      mode: '訂閱模式',
      modeHint: '自動：自動爬取站點首頁，站點RSS：通過站點RSS鏈接訂閱',
      rssInterval: '站點RSS週期',
      rssIntervalHint: '設置站點RSS運行週期，在訂閱模式為`站點RSS`時生效',
      filterRuleGroup: '訂閱優先級規則組',
      filterRuleGroupHint: '按選定的過濾規則組對訂閱進行過濾',
      bestVersionRuleGroup: '洗版優先級規則組',
      bestVersionRuleGroupHint: '按選定的過濾規則組對洗版訂閱進行過濾',
      timedSearch: '訂閱定時搜索',
      timedSearchHint: '每隔24小時全站搜索，以補全訂閱可能漏掉的資源',
      checkLocalMedia: '檢查文件系統資源',
      checkLocalMediaHint: '掃描存儲目錄中是否已存在相應資源文件，以避免重複下載；不管是否開啟都會檢查媒體伺服器',
      modes: {
        auto: '自動',
        rss: '站點RSS',
      },
      intervals: {
        min5: '5分鐘',
        min10: '10分鐘',
        min20: '20分鐘',
        min30: '半小時',
        hour1: '1小時',
        hour12: '12小時',
        day1: '1天',
      },
      saveSuccess: '訂閱站點保存成功',
      saveFailed: '訂閱站點保存失敗！',
      settingsSaveSuccess: '訂閱基礎設置保存成功',
      settingsSaveFailed: '訂閱基礎設置保存失敗！',
    },
    cache: {
      title: '緩存',
      description: '種子緩存、圖片文件緩存管理',
      subtitle: '管理緩存的站點資源',
      filterByTitle: '按標題篩選',
      filterBySite: '按站點篩選',
      selectSite: '選擇站點',
      refresh: '刷新緩存',
      deleteSelected: '刪除選中',
      clearAll: '清空緩存',
      refreshSuccess: '緩存刷新完成',
      refreshFailed: '刷新緩存失敗',
      clearSuccess: '緩存清理完成',
      clearFailed: '清理緩存失敗',
      deleteSuccess: '緩存項刪除成功',
      deleteFailed: '刪除緩存項失敗',
      deleteSelectedSuccess: '成功刪除 {count} 個緩存項',
      deleteSelectedFailed: '刪除緩存項失敗',
      loadFailed: '加載緩存數據失敗',
      selectDeleteWarning: '請選擇要刪除的緩存項',
      reidentify: '重新識別',
      reidentifySuccess: '重新識別完成',
      reidentifyFailed: '重新識別失敗',
      poster: '海報',
      torrentTitle: '標題',
      site: '站點',
      size: '大小',
      publishTime: '發布時間',
      recognitionResult: '識別結果',
      actions: '操作',
      unrecognized: '未識別',
      noData: '暫無緩存數據',
      noDataHint: '點擊"刷新緩存"按鈕獲取最新的種子緩存',
      reidentifyDialog: {
        title: '重新識別',
        torrentInfo: '種子信息',
        tmdbId: 'TMDB ID',
        tmdbIdHint: '可選，手動指定TMDB ID進行識別',
        doubanId: '豆瓣 ID',
        doubanIdHint: '可選，手動指定豆瓣ID進行識別',
        autoHint: '如果不指定ID，將自動重新識別該種子',
        cancel: '取消',
        confirm: '重新識別',
      },
      mediaType: {
        movie: '電影',
        tv: '電視劇',
      },
      clearConfirm: '確認清空所有緩存嗎？',
    },
  },
  dialog: {
    progress: {
      processing: '處理中',
    },
    subscribeSeason: {
      title: '訂閱 - {title}',
      selectGroup: '選擇劇集組',
      defaultGroup: '默認',
      seasonCount: '{count} 季',
      episodeCount: '{count} 集',
      seasonNumber: '第 {number} 季',
      airDate: '首播於 {date}',
      voteAverage: '{score}',
      status: {
        exists: '已入庫',
        partial: '部分缺失',
        missing: '缺失',
      },
      submit: '提交訂閱',
      selectSeasons: '請選擇訂閱季',
    },
    userAddEdit: {
      add: '添加用戶',
      edit: '編輯用戶',
      username: '用戶名',
      password: '密碼',
      confirmPassword: '確認密碼',
      email: '郵箱',
      nickname: '暱稱',
      status: '狀態',
      active: '激活',
      inactive: '已停用',
      superUser: '超級用戶',
      otp: '啟用二次驗證',
      avatar: '頭像',
      uploadAvatar: '上傳頭像',
      resetDefaultAvatar: '重置默認頭像',
      restoreCurrentAvatar: '還原當前頭像',
      notifications: '通知',
      wechat: '微信UserID',
      telegram: 'Telegram UserID',
      slack: 'Slack UserID',
      vocechat: 'VoceChat UserID',
      synologyChat: 'SynologyChat UserID',
      webPush: 'WebPush',
      creatingUser: '正在創建【{name}】用戶，請稍後',
      updatingUser: '正在更新【{name}】用戶，請稍後',
      usernameRequired: '用戶名不能為空',
      usernameExists: '用戶名已存在',
      passwordMismatch: '兩次輸入的密碼不一致',
      userCreated: '用戶【{name}】創建成功',
      userCreateFailed: '創建用戶失敗：{message}',
      userUpdateSuccess: '用戶【{name}】更新成功',
      userUpdateFailed: '更新用戶失敗：{message}',
      userDeleteSuccess: '用戶【{name}】刪除成功',
      userDeleteFailed: '刪除用戶失敗：{message}',
      invalidFile: '上傳的文件不符合要求，請重新選擇頭像',
      fileSizeLimit: '文件大小不得大於800KB',
      avatarUploadSuccess: '新頭像上傳成功，待保存後生效!',
      resetAvatarSuccess: '已重置為默認頭像，待保存後生效！',
      restoreAvatarSuccess: '已還原當前使用頭像！',
      deleteConfirm: '確認刪除用戶【{name}】嗎？',
      saveUserInfo: '保存用戶信息',
      cannotDeleteCurrentUser: '不能刪除當前登錄用戶',
      deleteUser: '刪除用戶',
      permissions: {
        title: '權限設置',
        presetNormal: '普通用戶',
        presetAdmin: '管理員',
        discovery: '發現',
        discoveryDesc: '存取推薦和探索功能',
        search: '搜索',
        searchDesc: '搜索站點資源和添加下載',
        subscribe: '訂閱',
        subscribeDesc: '管理電影和電視劇訂閱',
        manage: '管理',
        manageDesc: '存取下載管理和站點管理等功能',
      },
    },
    searchBar: {
      search: '搜索',
      searchPlaceholder: '搜索功能、訂閱、設置...',
      recentSearches: '最近搜索',
      noRecentSearches: '沒有最近搜索記錄',
      functions: '功能',
      noFunctionsFound: '沒有匹配的功能',
      plugins: '插件',
      noPluginsFound: '沒有匹配的插件',
      subscriptions: '訂閱',
      noSubscriptionsFound: '沒有匹配的訂閱',
      searchSites: '搜索站點',
      selectSites: '選擇站點',
      collections: '系列合集',
      collectionSearch: '相關的系列作品',
      actorSearch: '相關的演員、導演等',
      historySearch: '相關的歷史記錄',
      subscribeShareSearch: '相關的訂閱分享',
      siteResources: '站點資源',
      searchInSites: '在站點中搜索種子資源',
      relatedResources: '相關資源',
      searchTip: '可搜索電影、電視劇、演員、資源等',
    },
    searchSite: {
      selectSites: '選擇站點',
      siteSearch: '站點搜索',
      searchAllSites: '已選擇 {selected}/{total} 個站點',
      selectAll: '選擇全部',
      deselectAll: '取消全選',
      confirm: '確認',
      cancel: '取消',
    },
    importCode: {
      import: '導入',
      title: '導入代碼',
    },
    addDownload: {
      confirmDownload: '確認下載',
      downloader: '下載器（默認）',
      saveDirectory: '保存目錄（自動）',
      defaultPlaceholder: '留空默認',
      autoPlaceholder: '留空自動匹配',
      downloading: '下載中...',
      startDownload: '開始下載',
      downloadSuccess: '{site} {title} 下載成功！',
      downloadFailed: '{site} {title} 下載失敗：{message}！',
    },
    subscribeShare: {
      shareSubscription: '分享訂閱',
      season: '第 {number} 季',
      title: '標題',
      description: '說明',
      descriptionHint: '填寫關於該訂閱的說明，訂閱中的搜索詞、識別詞等將會默認包含在分享中',
      shareUser: '分享用戶',
      shareUserHint: '分享人的暱稱',
      confirmShare: '確認分享',
      shareSuccess: '{name} 分享成功！',
      shareFailed: '{name} 分享失敗：{message}！',
    },
    workflowShare: {
      shareWorkflow: '分享工作流',
      title: '標題',
      description: '說明',
      descriptionHint: '填寫關於該工作流的說明，工作流的動作和流程將會默認包含在分享中',
      shareUser: '分享用戶',
      shareUserHint: '分享人的暱稱',
      confirmShare: '確認分享',
      shareSuccess: '{name} 分享成功！',
      shareFailed: '{name} 分享失敗：{message}！',
      securityWarning: '安全提醒',
      securityWarningMessage: '分享前請確保工作流沒有敏感資訊，比如RSS連結中的PassKey等，避免產生資訊洩露。',
    },
    u115Auth: {
      loginTitle: '115網盤登錄',
      scanQrCode: '請使用微信或115客戶端掃碼',
      scanned: '已掃碼，請確認登錄',
      complete: '完成',
      reset: '重置',
    },
    aliyunAuth: {
      loginTitle: '阿里雲盤登錄',
      scanQrCode: '請用阿里雲盤 App 掃碼',
      scanned: '已掃碼',
      complete: '完成',
      reset: '重置',
    },
    rcloneConfig: {
      title: 'RClone配置',
      filePath: 'rclone配置文件路徑',
      fileContent: 'rclone配置文件內容',
      defaultContent: '# 請在此處填寫rclone配置文件內容 \n# 請參考 https://rclone.org/docs/ \n# 存儲節點名必須為：MP',
      complete: '完成',
      reset: '重置',
    },
    alistConfig: {
      title: 'OpenList配置',
      serverUrl: 'OpenList服務地址',
      username: '用戶名',
      password: '密碼',
      tokenUrl: '獲取Token地址',
      loginType: '登錄方式',
      loginTypeOptions: {
        guest: '訪客',
        username: '用戶名密碼',
        token: 'Token',
      },
      complete: '完成',
      reset: '重置',
    },
    smbConfig: {
      title: 'SMB網路共享配置',
      host: 'SMB伺服器地址',
      hostHint: 'SMB伺服器的IP地址或主機名',
      share: '共享名稱',
      shareHint: '要連接的共享資料夾名稱',
      username: '用戶名',
      usernameHint: 'SMB登入用戶名',
      password: '密碼',
      passwordHint: 'SMB登入密碼',
      domain: '域名',
      domainHint: 'SMB域名，如WORKGROUP或域控制器名稱',
      complete: '完成',
      reset: '重置',
    },
    workflowAddEdit: {
      addTitle: '新增工作流',
      editTitle: '編輯工作流',
      name: '名稱',
      namePlaceholder: '工作流名稱',
      desc: '描述',
      descPlaceholder: '工作流描述',
      enabled: '啟用',
      triggerType: '觸發類型',
      triggerTypeTimer: '定時觸發',
      triggerTypeEvent: '事件觸發',
      triggerTypeManual: '手動觸發',
      schedule: '定時執行',
      cronExpr: 'Cron表達式',
      cronExprDesc: '工作流定時執行的cron表達式',
      eventType: '事件類型',
      eventTypePlaceholder: '請選擇事件類型',
      nameRequired: '請填寫完整資訊！',
      triggerRequired: '請選擇觸發類型！',
      timerRequired: '請填寫定時表達式！',
      eventTypeRequired: '請選擇事件類型！',
      addSuccess: '建立任務成功，請編輯流程！',
      addFailed: '建立任務失敗：{message}',
      editSuccess: '修改任務成功！',
      editFailed: '修改任務失敗：{message}',
      cancel: '取消',
      confirm: '確認',
    },
    workflowActions: {
      title: '編輯流程',
      noActionsMessage: '工作流沒有動作，請新增動作',
      addAction: '新增動作',
      editAction: '編輯動作',
      deleteAction: '刪除動作',
      moveUp: '上移',
      moveDown: '下移',
      nameLabel: '動作名稱',
      nameRequired: '動作名稱不能為空',
      typeLabel: '動作類型',
      typeRequired: '動作類型不能為空',
      paramsLabel: '動作參數',
      outputLabel: '動作輸出',
      saveAction: '儲存動作',
      cancelAction: '取消',
      confirmDeleteTitle: '確認刪除動作',
      confirmDeleteMessage: '確定要刪除此動作嗎？此操作無法復原。',
      yesDelete: '是的，刪除',
      noCancel: '取消',
      invalidConnection: '非法連接：不能連接自身或同類型端口！',
      componentNotFound: '組件 {component} 未找到',
      componentAdded: '已新增組件到畫布',
      saveSuccess: '儲存任務流程成功！',
      saveFailed: '儲存任務流程失敗：{message}',
      importTitle: '匯入任務流程',
      importSuccess: '匯入成功！',
      importFailed: '匯入失敗！',
      codeCopied: '任務流程代碼已複製到剪貼簿！',
    },
    siteCookieUpdate: {
      title: '更新站點Cookie & UA',
      processing: '請稍候...',
      updating: '正在更新 {site} Cookie & UA...',
      success: '{site} 更新Cookie & UA成功！',
      failed: '{site} 更新失敗：{message}',
      updateButton: '開始更新',
    },
    siteAddEdit: {
      addTitle: '新增站點',
      editTitle: '編輯站點',
      nameLabel: '站點名稱',
      urlLabel: '站點URL',
      iconLabel: '站點圖標',
      uploadIcon: '上傳圖標',
      cookie: 'Cookie',
      rssUrl: 'RSS連結',
      enableLabel: '啟用',
      pubEnableLabel: '資源公開',
      priorityLabel: '優先級',
      signInLabel: '簽到',
      proxies: '代理',
      userInfo: '用戶資訊',
      cancel: '取消',
      confirm: '儲存',
    },
    pluginConfig: {
      title: '插件配置',
      save: '儲存',
      close: '關閉',
      viewData: '查看數據',
      saving: '正在儲存 {name} 配置...',
      saveSuccess: '插件 {name} 配置已儲存',
      saveFailed: '插件 {name} 配置儲存失敗：{message}',
    },
    pluginData: {
      title: '插件數據',
      save: '儲存',
      close: '關閉',
    },
    pluginMarketSetting: {
      title: '插件市場設置',
      repoUrl: '插件倉庫地址',
      repoPlaceholder: '格式：https://github.com/jxxghp/MoviePilot-Plugins/,https://github.com/xxxx/xxxxxx/',
      repoHint: '多個地址使用换行分隔，僅支援Github倉庫',
      close: '關閉',
      save: '儲存',
      saveSuccess: '插件倉庫儲存成功',
      saveFailed: '插件倉庫儲存失敗：{message}！',
    },
    userAuth: {
      title: '用戶認證',
      codeLabel: '認證碼',
      codePlaceholder: '請輸入認證碼',
      authBtn: '開始認證',
      closeBtn: '關閉',
      selectSite: '選擇認證站點',
      selectSiteRequired: '請選擇認證站點！',
      siteConfigNotExist: '站點配置不存在！',
      fieldRequired: '請輸入{name}！',
      authSuccess: '用戶認證成功，請重新登入！',
      authFailed: '認證失敗：{message}',
    },
    transferQueue: {
      title: '整理隊列',
      name: '名稱',
      type: '類型',
      state: '狀態',
      progress: '進度',
      startTime: '開始時間',
      speedTitle: '速度',
      pathTitle: '路徑',
      sizeTitle: '大小',
      waitingState: '等待中',
      runningState: '正在整理',
      finishedState: '完成',
      failedState: '失敗',
      cancelledState: '已取消',
      noTasks: '沒有正在整理的任務',
      processing: '請稍候 ...',
      stopAll: '全部停止',
      startAll: '全部開始',
      refresh: '刷新',
      close: '關閉',
    },
    reorganize: {
      title: '整理',
      sourceTitle: '源文件',
      targetTitle: '目標文件',
      processingTitle: '處理中',
      confirmTitle: '確認',
      selectFile: '選擇文件',
      selectTarget: '選擇目標',
      selectMediaType: '選擇媒體類型',
      movie: '電影',
      tv: '電視劇',
      selectTmdbId: '選擇TMDB ID',
      selectMediaInfo: '選擇媒體資訊',
      selectTargetPath: '選擇目標路徑',
      selectTargetDir: '選擇目標目錄',
      selectFileName: '選擇文件名',
      confirmMoving: '請確認移動！',
      sourceLabel: '源文件：',
      targetLabel: '目標目錄：',
      filenameLabel: '文件名：',
      close: '關閉',
      next: '下一步',
      previous: '上一步',
      confirm: '確認',
      manualTitle: '手動整理',
      multipleItemsTitle: '共 {count} 項',
      singleItemTitle: '{path}',
      targetStorage: '目的存儲',
      targetStorageHint: '整理目的存儲',
      transferType: '整理方式',
      transferTypeHint: '文件操作整理方式',
      targetPath: '目的路徑',
      targetPathHint: '整理目的路徑，留空將自動匹配',
      targetPathPlaceholder: '留空自動',
      mediaType: '類型',
      mediaTypeHint: '文件的媒體類型',
      tmdbId: 'TheMovieDb編號',
      doubanId: '豆瓣編號',
      mediaIdHint: '按名稱查詢媒體編號，留空自動識別',
      mediaIdPlaceholder: '留空自動識別',
      episodeGroup: '劇集組編號',
      episodeGroupHint: '指定劇集組',
      episodeGroupPlaceholder: '手動查詢劇集組',
      season: '季',
      seasonHint: '第幾季',
      episodeDetail: '集',
      episodeDetailHint: '集數或範圍，如1或1,2',
      episodeDetailPlaceholder: '起始集,終止集',
      episodeFormat: '集數定位',
      episodeFormatHint: '使用{ep}定位文件名中的集數部分以輔助識別',
      episodeFormatPlaceholder: '使用{ep}定位集數',
      episodeOffset: '集數偏移',
      episodeOffsetHint: '集數偏移運算，如-10或EP*2',
      episodeOffsetPlaceholder: '如-10',
      episodePart: '指定Part',
      episodePartHint: '指定Part，如part1',
      episodePartPlaceholder: '如part1',
      minFileSize: '最小文件大小（MB）',
      minFileSizeHint: '只整理大於最小文件大小的文件',
      typeFolderOption: '按類型分類',
      typeFolderHint: '整理時目的路徑下按媒體類型新增子目錄',
      categoryFolderOption: '按類別分類',
      categoryFolderHint: '整理時在目的路徑下按媒體類別新增子目錄',
      scrapeOption: '刮削元數據',
      scrapeHint: '整理完成後自動刮削元數據',
      fromHistoryOption: '復用歷史識別資訊',
      fromHistoryHint: '使用歷史整理記錄中已識別的媒體資訊',
      addToQueue: '加入整理隊列',
      reorganizeNow: '立即整理',
      auto: '自動',
      processing: '正在處理 ...',
      successMessage: '文件 {name} 已加入整理隊列！',
    },
    subscribeEdit: {
      titleDefault: '默認訂閱規則',
      titleEdit: '編輯訂閱',
      seasonFormat: '第 {number} 季',
      tabs: {
        basic: '基礎',
        advance: '進階',
      },
      searchKeyword: '搜索關鍵詞',
      searchKeywordHint: '指定搜索站點時使用的關鍵詞',
      totalEpisode: '總集數',
      totalEpisodeHint: '劇集總集數',
      startEpisode: '開始集數',
      startEpisodeHint: '開始訂閱集數',
      quality: '質量',
      qualityHint: '訂閱資源質量',
      resolution: '分辨率',
      resolutionHint: '訂閱資源分辨率',
      effect: '特效',
      effectHint: '訂閱資源特效',
      subscribeSites: '訂閱站點',
      subscribeSitesHint: '訂閱的站點範圍，不選使用系統設置',
      downloader: '下載器',
      downloaderHint: '指定該訂閱使用的下載器',
      savePath: '儲存路徑',
      savePathHint: '指定該訂閱的下載儲存路徑，留空自動使用設定的下載目錄',
      bestVersion: '洗版',
      bestVersionHint: '根據洗版優先級進行洗版訂閱',
      searchImdbid: '使用 ImdbID 搜索',
      searchImdbidHint: '開使用 ImdbID 精確搜索資源',
      showEditDialog: '訂閱時編輯更多規則',
      showEditDialogHint: '新增訂閱時顯示此編輯訂閱對話框',
      include: '包含（關鍵字、正則式）',
      includeHint: '包含規則，支援正則表達式',
      exclude: '排除（關鍵字、正則式）',
      excludeHint: '排除規則，支援正則表達式',
      filterGroups: '優先級規則組',
      filterGroupsHint: '按選定的過濾規則組對訂閱進行過濾',
      episodeGroup: '指定劇集組',
      episodeGroupHint: '按特定劇集組識別和刮削',
      season: '指定季',
      seasonHint: '指定任意季訂閱',
      mediaCategory: '自定義類別',
      mediaCategoryHint: '指定類別名稱，留空自動識別',
      customWords: '自定義識別詞',
      customWordsHint: '只對該訂閱使用的識別詞',
      customWordsPlaceholder:
        '屏蔽詞\n被替換詞 => 替換詞\n前定位詞 <> 後定位詞 >> 集偏移量（EP）\n被替換詞 => 替換詞 && 前定位詞 <> 後定位詞 >> 集偏移量（EP）\n其中替換詞支援格式：&#123; tmdbid/doubanid=xxx;type=movie/tv;s=xxx;e=xxx &#125; 直接指定TMDBID/豆瓣ID識別，其中s、e為季數和集數（可選）',
      cancelSubscribe: '取消訂閱',
      save: '儲存',
      cancelSubscribeConfirm: '是否確認取消訂閱？',
    },
    subscribeFiles: {
      title: '已下載文件',
      noFilesMessage: '暫無文件',
      close: '關閉',
      downloadTab: '下載文件',
      libraryTab: '媒體庫文件',
      episodeColumn: '集',
      torrentColumn: '種子',
      fileColumn: '文件',
      itemsPerPage: '每頁條數',
      pageText: '{0}-{1} 共 {2} 條',
      loadingText: '載入中...',
      noData: '沒有數據',
      season: '第 {number} 季',
    },
    subscribeHistory: {
      title: '{type}訂閱歷史',
      resubscribe: '重新訂閱',
      resubscribeMovie: '正在重新訂閱 {name}...',
      resubscribeTv: '正在重新訂閱 {name} 第 {season} 季...',
      season: '第 {season} 季',
      noData: '沒有已完成的訂閱',
    },
    siteUserData: {
      title: '站點用戶數據',
      updateTime: '更新時間',
      username: '用戶名',
      uploadTitle: '上傳量',
      uploadTotal: '總上傳量',
      downloadTitle: '下載量',
      downloadTotal: '總下載量',
      seedingTitle: '做種數',
      seedingCount: '總做種數',
      seedingSize: '總做種體積',
      userLevel: '用戶等級',
      msgCount: '未讀消息',
      inviteCount: '邀請數',
      bonus: '積分',
      ratio: '分享率',
      joinTime: '加入時間',
      trafficHistory: '歷史流量',
      seedingDistribution: '做種分佈',
      volumeTitle: '體積',
      countTitle: '數量：',
      noData: '無',
      refreshing: '正在刷新站點數據...',
      close: '關閉',
    },
    siteResource: {
      browseTitle: '瀏覽 - {name}',
      searchKeyword: '搜索關鍵字',
      resourceCategory: '資源分類',
      search: '搜索',
      itemsPerPage: '每頁條數',
      pageText: '{0}-{1} 共 {2} 條',
      noData: '沒有數據',
      loading: '加載中...',
      titleColumn: '標題',
      timeColumn: '時間',
      sizeColumn: '大小',
      seedersColumn: '做種',
      peersColumn: '下載',
      viewDetails: '查看詳情',
      downloadTorrent: '下載種子文件',
    },
    forkSubscribe: {
      title: '複製訂閱',
      selectSubscriber: '選擇複製目標',
      overwriteExisting: '覆蓋現有訂閱',
      overwriteExistingHint: '目標用戶已存在該訂閱時，是否覆蓋',
      confirm: '確認',
      cancel: '取消',
    },
  },
  file: {
    newFolder: '新建文件夾',
    autoRecognizeName: '自動識別名稱',
    createFolder: '創建文件夾',
    fileName: '文件名',
    fileSize: '文件大小',
    fileType: '文件類型',
    lastModified: '修改時間',
    actions: '操作',
    rename: '重命名',
    delete: '刪除',
    confirmFileDelete: '確認刪除',
    upload: '上傳',
    download: '下載',
    preview: '預覽',
    selectAll: '全選',
    deselectAll: '取消全選',
    moveUp: '返回上一級',
    sortByName: '按名稱排序',
    sortByTime: '按時間排序',
    currentName: '當前名稱',
    newName: '新名稱',
    includeSubfolders: '自動重命名目錄內所有媒體文件',
    emptyFolder: '空文件夾',
    noFilesInFolder: '該文件夾內沒有文件',
    autoRecognize: '自動識別名稱',
    directoryTree: '目錄樹',
    rootDirectory: '根目錄',
    noDirectories: '沒有可用的目錄',
    directory: '目錄',
    file: '文件',
    size: '大小',
    modifyTime: '修改時間',
    noFiles: '沒有目錄或文件',
    emptyDirectory: '空目錄',
    confirmDelete: '是否確認刪除{type} {name}？',
    confirmBatchDelete: '是否確認刪除選中的 {count} 個項目？',
    deleting: '正在刪除 {name}...',
    recognize: '識別',
    recognizing: '正在識別 {path}...',
    recognizeFailed: '{path} 識別失敗！',
    scrape: '刮削',
    scraping: '正在刮削 {path}...',
    scrapeCompleted: '{path} 削刮完成！',
    confirmScrape: '是否確認刮削 {path}？',
    confirmBatchScrape: '是否確認刮削選中的 {count} 項？',
    renaming: '正在重命名 {name}...',
    renamingAll: '正在重命名 {path} 及目錄內所有文件...',
    close: '關閉',
    loadingDirectoryStructure: '加載目錄結構...',
    reorganize: '整理',
  },
  person: {
    alias: '別名：',
    credits: '參演作品',
    biography: '個人簡介',
    birthday: '出生日期',
    placeOfBirth: '出生地',
  },
  error: {
    title: '出錯啦！',
    networkError: '無法獲取到媒體信息，請檢查網絡連接。',
    serverError: '服務器錯誤，請稍後重試。',
    notFound: '找不到請求的資源。',
  },
  plugin: {
    sort: {
      popular: '熱門',
      name: '插件名稱',
      author: '作者',
      repository: '插件倉庫',
      latest: '最新發布',
    },
    installingPlugin: '正在安装插件...',
    installing: '正在安装 {name} v{version} ...',
    installSuccess: '插件 {name} 安装成功！',
    installFailed: '插件 {name} 安装失败：{message}',
    filterPlugins: '過濾插件',
    name: '名稱',
    hasNewVersion: '有新版本',
    running: '運行中',
    author: '作者',
    label: '標籤',
    repository: '倉庫',
    sortTitle: '排序',
    filter: '過濾：{name}',
    noMatchingContent: '沒有找到匹配的內容',
    pleaseInstallFromMarket: '請從插件市場安裝插件',
    allPluginsInstalled: '所有插件已安裝',
    searchPlugins: '搜索插件',
    searchPlaceholder: '按插件名稱或描述搜索',
    uninstalling: '正在卸載 {name} ...',
    uninstallSuccess: '插件 {name} 卸载成功！',
    uninstallFailed: '插件 {name} 卸载失败：{message}',
    updating: '正在更新 {name} ...',
    updateSuccess: '插件 {name} 更新成功！',
    updateFailed: '插件 {name} 更新失敗：{message}',
    noPlugins: '沒有安裝插件',
    installed: '已安裝',
    notInstalled: '未安裝',
    hasUpdate: '可更新',
    configuring: '配置中',
    enable: '啟用',
    disable: '禁用',
    settings: '設置',
    projectHome: '項目主頁',
    updateHistory: '更新說明',
    installToLocal: '安裝到本地',
    totalDownloads: '共 {count} 次下載',
    viewData: '查看數據',
    update: '更新',
    reset: '重置',
    uninstall: '卸載',
    viewLogs: '查看日誌',
    authorHome: '作者主頁',
    confirmUninstall: '是否確認卸載插件 {name}？',
    confirmReset: '此操作將恢復插件 {name} 的默認設置，並清除所有相關數據，確定要繼續嗎？',
    resetSuccess: '插件 {name} 數據已重置',
    resetFailed: '插件 {name} 重置失敗：{message}',
    updateHistoryTitle: '{name} 更新說明',
    updateToLatest: '更新到最新版本',
    updatingTo: '正在更新 {name} 至 v{version} ...',
    folderNameEmpty: '文件夾名稱不能為空',
    folderExists: '文件夾已存在',
    folderCreateSuccess: '文件夾創建成功',
    folderRenameSuccess: '文件夾重命名成功',
    folderRenameFailed: '重命名文件夾失敗',
    folderDeleteSuccess: '文件夾刪除成功',
    folderDeleteFailed: '刪除文件夾失敗',
    removeFromFolderSuccess: '插件已移出文件夾',
    operationFailed: '操作失敗',
    saveFolderConfigFailed: '保存文件夾配置失敗',
    newFolder: '新建文件夾',
    folderName: '文件夾名稱',
    cancel: '取消',
    create: '創建',
    clone: '分身',
    cloneTitle: '創建插件分身',
    cloneSubtitle: '為 {name} 創建獨立的分身實例',
    cloneFeature: '插件分身功能',
    cloneDescription: '創建插件的獨立副本，擁有獨立的配置和數據，適用於多賬號、測試環境等場景',
    suffix: '分身後綴',
    suffixPlaceholder: '例如：Test、Backup、Site1',
    suffixHint: '用於區分分身的唯一標識，只能包含英文字母和數字',
    suffixRequired: '分身後綴不能為空',
    suffixFormatError: '只能包含英文字母和數字',
    suffixLengthError: '長度不能超過20個字符',
    cloneName: '分身名稱',
    cloneNamePlaceholder: '例如：自動備份 測試版',
    cloneNameHint: '分身插件的顯示名稱（可選）',
    cloneDefaultName: '{name} 分身',
    cloneDescriptionLabel: '分身描述',
    cloneDescriptionPlaceholder: '描述這個分身的用途和特點...',
    cloneDescriptionHint: '詳細描述分身插件的用途（可選）',
    cloneDefaultDescription: '{description} (分身版本)',
    cloneVersion: '版本號',
    cloneVersionPlaceholder: '例如：1.0、2.1.0',
    cloneVersionHint: '自定義分身插件的版本號（可選）',
    cloneIcon: '圖標URL',
    cloneIconPlaceholder: 'https://example.com/icon.png',
    cloneIconHint: '自定義分身插件的圖標（可選）',
    cloneNotice: '分身插件創建後默認為禁用狀態，需要手動配置啟用。分身後綴一旦確定無法修改。',
    createClone: '創建分身',
    cloning: '正在創建 {name} 的分身...',
    cloneSuccess: '插件分身 {name} 創建成功！',
    cloneFailed: '插件分身創建失敗：{message}',
    cloneFailedGeneral: '插件分身創建失敗',
    logTitle: '插件日誌',
    quickAccess: '快速訪問',
    noPluginsWithPage: '暫無可展示的插件',
    tapToOpen: '點擊返回主界面',
    recentlyUsed: '最近使用',
    allPlugins: '所有插件',
    noRecentPlugins: '無',
  },
  profile: {
    personalInfo: '個人信息',
    uploadNewAvatar: '上傳新頭像',
    avatarFormatError: '上傳的文件不符合要求，請重新選擇頭像',
    avatarSizeError: '文件大小不得大於800KB',
    avatarUploadSuccess: '新頭像上傳成功，待保存後生效!',
    resetAvatarSuccess: '已重置為默認頭像，待保存後生效！',
    restoreAvatarSuccess: '已還原當前使用頭像！',
    savingInProgress: '正在保存中，請稍後...',
    usernameRequired: '用戶名不能為空',
    passwordMismatch: '兩次輸入的密碼不一致',
    usernameChangeSuccess: '【{oldName}】更名【{newName}】，用戶信息保存成功！',
    saveSuccess: '用戶信息保存成功！',
    saveFailedWithNameChange: '【{oldName}】更名【{newName}】，信息保存失敗：{message}！',
    saveFailed: '用戶信息保存失敗：{message}！',
    nickname: '暱稱',
    nicknamePlaceholder: '顯示暱稱，優先於用戶名顯示',
    accountBinding: '賬號綁定',
    wechatUser: '微信用戶',
    telegramUser: 'Telegram用戶',
    slackUser: 'Slack用戶',
    vocechatUser: 'VoceChat用戶',
    synologychatUser: 'SynologyChat用戶',
    doubanUser: '豆瓣用戶',
    twoFactorAuthentication: '登錄雙重驗證',
    enableTwoFactor: '開啟雙重驗證',
    disableTwoFactor: '關閉雙重驗證',
    otpGenerateFailed: '獲取otp uri失敗：{message}！',
    otpDisableSuccess: '關閉登錄雙重驗證成功！',
    otpDisableFailed: '關閉otp失敗：{message}！',
    otpCodeRequired: '請填寫6位驗證碼',
    otpEnableSuccess: '開啟登錄雙重驗證成功！',
    otpEnableFailed: '開啟otp失敗：{message}！',
    authenticatorApp: '身份驗證器',
    authenticatorAppDescription:
      '使用像Google Authenticator、Microsoft Authenticator、Authy或1Password這樣的身份驗證器應用程序，掃描二維碼。它將為您生成一個6位數的代碼，供您在下方輸入。',
    secretKeyTip: '如果您在使用二維碼時遇到困難，請在您的應用程序中選擇手動輸入以上代碼。',
    enterVerificationCode: '輸入驗證碼以確認開啟雙重驗證',
    avatarFormatTip: '允許 JPG、PNG、GIF、WEBP 格式， 最大尺寸 800KB。',
  },
  transferHistory: {
    title: '轉移歷史',
    searchPlaceholder: '搜索轉移記錄',
    titleColumn: '標題',
    pathColumn: '路徑',
    modeColumn: '轉移方式',
    sizeColumn: '大小',
    dateColumn: '時間',
    statusColumn: '狀態',
    actionsColumn: '操作',
    seasonEpisode: '季集/類別',
    transferQueue: '轉移隊列',
    groupMode: '分組模式',
    listMode: '列表模式',
    deleteConfirm: '確認刪除 {title} {seasons}{episodes}?',
    deleteConfirmBatch: '確認刪除 {count} 條記錄?',
    deleteRecordOnly: '僅刪除轉移記錄',
    deleteSourceOnly: '刪除轉移記錄和源文件',
    deleteDestOnly: '刪除轉移記錄和媒體庫文件',
    deleteAll: '刪除所有',
    transferMode: {
      copy: '複製',
      move: '移動',
      link: '硬鏈接',
      softlink: '軟鏈接',
      rclone_copy: 'Rclone複製',
      rclone_move: 'Rclone移動',
    },
    status: {
      success: '成功',
      failed: '失敗',
      unknown: '未知',
    },
    noData: '沒有數據',
    loading: '加載中...',
    pageSize: '每頁條數',
    pageInfo: '{begin} - {end} / {total}',
    actions: {
      redo: '重新整理',
      delete: '刪除',
    },
    progress: {
      processing: '處理中',
      pleaseWait: '請稍候...',
    },
    table: {
      emptyTitle: '操作',
    },
  },
  customRule: {
    error: {
      emptyIdName: '規則ID和規則名稱不能為空',
      idOccupied: '當前規則ID已被內置規則佔用',
      nameOccupied: '當前規則名稱已被內置規則佔用',
      idExists: '規則ID【{id}】已存在',
      nameExists: '規則名稱【{name}】已存在',
    },
    title: '{id} - 配置',
    field: {
      ruleId: '規則ID',
      ruleName: '規則名稱',
      include: '包含',
      exclude: '排除',
      sizeRange: '資源體積（MB）',
      seeders: '做種人數',
      publishTime: '發佈時間（分鐘）',
    },
    placeholder: {
      ruleId: '必填；不可與其他規則ID重名',
      ruleName: '必填；不可與其他規則名稱重名',
      include: '關鍵詞/正則表達式',
      exclude: '關鍵詞/正則表達式',
      sizeRange: '0/1-10',
      seeders: '0/1-10',
      publishTime: '0/1-10',
    },
    hint: {
      ruleId: '字符與數字組合，不能含空格',
      ruleName: '使用別名便於區分規則',
      include: '必須包含的關鍵詞或正則表達式，多個值使用｜分隔',
      exclude: '不能包含的關鍵詞或正則表達式，多個值使用｜分隔',
      sizeRange: '最小資源文件體積或體積範圍（劇集計算單集平均大小）',
      seeders: '最小做種人數或做種人數範圍',
      publishTime: '距離資源發佈的最小時間間隔或時間區間',
    },
    action: {
      confirm: '確定',
    },
  },
  downloader: {
    title: '下載器',
    name: '名稱',
    type: '類型',
    customTypeHint: '自定義下載器類型，用於插件等場景',
    enabled: '啟用',
    default: '預設',
    host: '地址',
    username: '用戶名',
    password: '密碼',
    category: '自動分類管理',
    sequentail: '順序下載',
    force_resume: '強制繼續',
    first_last_piece: '優先首尾文件',
    saveSuccess: '下載器設置保存成功',
    saveFailed: '下載器設置保存失敗',
    nameRequired: '名稱不能為空',
    nameDuplicate: '名稱已存在',
    defaultChanged: '存在預設下載器，已替換',
  },
  filterRule: {
    title: '過濾規則',
    groupName: '規則組名稱',
    priority: '優先級',
    rules: '規則',
    add: '添加規則',
    import: '導入規則',
    share: '分享規則',
    save: '保存規則',
    nameRequired: '規則組名稱不能為空',
    nameDuplicate: '規則組名稱已存在',
    importSuccess: '規則導入成功',
    importFailed: '規則導入失敗',
    shareSuccess: '規則已複製到剪貼板',
    shareFailed: '規則複製失敗',
    mediaType: '媒體類型',
    category: '媒體類別',
    mediaTypeItems: {
      movie: '電影',
      tv: '電視劇',
      anime: '動漫',
      collection: '合集',
      unknown: '未知',
    },
  },
  mediaserver: {
    type: '類型',
    customTypeHint: '自定義媒體伺服器類型，用於插件等場景',
    enableMediaServer: '啟用媒體伺服器',
    nameRequired: '必填；不可與其他名稱重名',
    serverAlias: '媒體伺服器的別名',
    host: '地址',
    hostPlaceholder: 'http(s)://ip:port',
    hostHint: '服務端地址，格式：http(s)://ip:port',
    playHost: '外網播放地址',
    playHostPlaceholder: 'http(s)://domain:port',
    playHostHint: '跳轉播放頁面使用的地址，格式：http(s)://domain:port',
    apiKey: 'API密鑰',
    embyApiKeyHint: 'Emby設置->高級->API密鑰中生成的密鑰',
    jellyfinApiKeyHint: 'Jellyfin設置->高級->API密鑰中生成的密鑰',
    plexToken: 'X-Plex-Token',
    plexTokenHint: '瀏覽器F12->網絡，從Plex請求URL中獲取的X-Plex-Token',
    username: '用戶名',
    password: '密碼',
    syncLibraries: '同步媒體庫',
    syncLibrariesHint: '只有選中的媒體庫才會被同步',
    nameExists: '【{name}】已存在，請替換為其他名稱',
  },
  bangumi: {
    category: '類別',
    sort: '排序',
    year: '年份',
    cat: {
      other: '其他',
      tv: 'TV',
      ova: 'OVA',
      movie: 'Movie',
      web: 'WEB',
    },
    sortType: {
      rank: '排名',
      date: '日期',
    },
  },
  tmdb: {
    type: '類型',
    sort: '排序',
    genre: '風格',
    language: '語言',
    rating: '評分',
    sortType: {
      popularityDesc: '熱度降序',
      popularityAsc: '熱度升序',
      releaseDateDesc: '上映日期降序',
      releaseDateAsc: '上映日期升序',
      firstAirDateDesc: '首播日期降序',
      firstAirDateAsc: '首播日期升序',
      voteAverageDesc: '評分降序',
      voteAverageAsc: '評分升序',
    },
    genreType: {
      action: '動作',
      adventure: '冒險',
      animation: '動畫',
      comedy: '喜劇',
      crime: '犯罪',
      documentary: '紀錄片',
      drama: '劇情',
      family: '家庭',
      fantasy: '奇幻',
      history: '歷史',
      horror: '恐怖',
      music: '音樂',
      mystery: '懸疑',
      romance: '愛情',
      scienceFiction: '科幻',
      tvMovie: '電視電影',
      thriller: '驚悚',
      war: '戰爭',
      western: '西部',
      actionAdventure: '動作冒險',
      kids: '兒童',
      news: '新聞',
      reality: '真人秀',
      sciFiFantasy: '科幻奇幻',
      soap: '肥皂劇',
      talk: '戲劇',
      warPolitics: '戰爭政治',
    },
    languageType: {
      zh: '中文',
      en: '英語',
      ja: '日語',
      ko: '韓語',
      fr: '法語',
      de: '德語',
      es: '西班牙語',
      it: '意大利語',
      ru: '俄語',
      pt: '葡萄牙語',
      ar: '阿拉伯語',
      hi: '印地語',
      th: '泰語',
    },
  },
  douban: {
    type: '類型',
    sort: '排序',
    genre: '風格',
    zone: '地區',
    year: '年代',
    sortType: {
      comprehensive: '綜合排序',
      releaseDate: '首播時間',
      recentHot: '近期熱度',
      highScore: '高分優先',
    },
    genreType: {
      comedy: '喜劇',
      romance: '愛情',
      action: '動作',
      scienceFiction: '科幻',
      animation: '動畫',
      mystery: '懸疑',
      crime: '犯罪',
      thriller: '驚悚',
      adventure: '冒險',
      music: '音樂',
      history: '歷史',
      fantasy: '奇幻',
      horror: '恐怖',
      war: '戰爭',
      biography: '傳記',
      musical: '歌舞',
      martialArts: '武俠',
      erotic: '情色',
      disaster: '災難',
      western: '西部',
      documentary: '紀錄片',
      shortFilm: '短片',
    },
    zoneType: {
      chinese: '華語',
      europeanAmerican: '歐美',
      korean: '韓國',
      japanese: '日本',
      mainlandChina: '中國大陸',
      usa: '美國',
      hongKong: '中國香港',
      taiwan: '中國台灣',
      uk: '英國',
      france: '法國',
      germany: '德國',
      italy: '義大利',
      spain: '西班牙',
      india: '印度',
      thailand: '泰國',
      russia: '俄羅斯',
      canada: '加拿大',
      australia: '澳大利亞',
      ireland: '愛爾蘭',
      sweden: '瑞典',
      brazil: '巴西',
      denmark: '丹麥',
    },
    yearType: {
      '2020s': '2020年代',
      '2010s': '2010年代',
      '2000s': '2000年代',
      '1990s': '90年代',
      '1980s': '80年代',
      '1970s': '70年代',
      '1960s': '60年代',
    },
  },
  directory: {
    alias: '目錄別名',
    mediaType: '媒體類型',
    mediaCategory: '媒體分類',
    resourceStorage: '資源存儲',
    resourceDirectory: '資源目錄',
    sortByType: '按類型排序',
    sortByCategory: '按分類排序',
    autoTransfer: '自動轉移',
    monitorMode: '監控模式',
    libraryStorage: '媒體庫存儲',
    libraryDirectory: '媒體庫目錄',
    transferType: '轉移方式',
    overwriteMode: '覆蓋模式',
    smartRename: '智能重命名',
    scrapingMetadata: '刮削元數據',
    sendNotification: '發送通知',
    noTransfer: '不轉移',
    downloaderMonitor: '下載器監控',
    directoryMonitor: '目錄監控',
    manualTransfer: '手動轉移',
    performanceMode: '性能模式',
    compatibilityMode: '兼容模式',
    pleaseSelectStorage: '請選擇存儲',
    pleaseSelectLibraryStorage: '請選擇媒體庫存儲',
    pleaseSelectDownloadStorage: '請選擇下載存儲',
    noSupportedTransferType: '無支持的轉移方式',
    never: '從不',
    always: '總是',
    byFileSize: '按文件大小',
    keepLatestOnly: '僅保留最新',
  },
  validators: {
    required: '此項為必填項',
    number: '請輸入數字',
  },
  folder: {
    settingAppearance: '設定外觀',
    rename: '重新命名',
    deleteFolder: '刪除資料夾',
    folderNameCannotBeEmpty: '資料夾名稱不能為空',
    confirmDeleteFolder: '確定要刪除資料夾 "{folderName}" 嗎？資料夾中的插件將移回主列表。',
    folderSettingsSaved: '資料夾設定已儲存',
    renameFolder: '重新命名資料夾',
    folderName: '資料夾名稱',
    folderAppearanceSettings: '資料夾外觀設定',
    showFolderIcon: '顯示資料夾圖示',
    icon: '圖示',
    iconColor: '圖示顏色',
    backgroundGradient: '背景漸變',
    customBackgroundImageURL: '自定義背景圖片URL（可選）',
    customBackgroundImageHint: '支援網路圖片URL，留空則使用漸變背景',
    pluginCount: '{count} 個插件',
  },
}
