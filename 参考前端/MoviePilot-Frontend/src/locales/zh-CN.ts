export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    close: '关闭',
    version: '版本',
    author: '作者',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    loading: '加载中',
    success: '成功',
    error: '错误',
    openInNewWindow: '在新窗口中打开',
    inputMessage: '输入消息或命令',
    send: '发送',
    noData: '暂无数据',
    noContent: '没有找到相关内容',
    all: '全部',
    active: '激活',
    inactive: '未激活',
    filter: '筛选',
    noMatchingData: '没有符合条件的数据',
    tryChangingFilters: '请尝试更改筛选条件',
    default: '默认',
    name: '名称',
    create: '新建',
    saving: '保存中',
    reset: '重置',
    theme: '主题',
    language: '语言',
    pleaseWait: '请稍候...',
    viewDetails: '查看详情',
    user: '用户',
    config: '配置',
    pause: '暂停',
    enable: '启用',
    confirmAction: '确认{action}',
    details: '详情',
    files: '文件',
    share: '分享',
    subscribe: '订阅',
    unsubscribe: '取消订阅',
    media: '媒体',
    unknown: '未知',
    notice: '注意',
    itemsPerPage: '每页条数',
    pageText: '{0}-{1} 共 {2} 条',
    noDataText: '没有数据',
    loadingText: '加载中...',
    networkRequired: '此功能需要网络连接',
    networkDisconnected: '网络连接已断开',
    featuresLimited: '部分功能可能受限',
    serverConnectionFailed: '服务器连接失败',
    troubleshooting: '疑难解答',
    checking: '检查中',
    retry: '重试',
    networkOnline: '网络在线',
    networkOffline: '网络离线',
    serviceAvailable: '服务可用',
    serviceUnavailable: '服务不可用',
    status: '状态',
    preset: '预设',
  },
  mediaType: {
    movie: '电影',
    tv: '电视剧',
    anime: '动漫',
    collection: '合集',
    unknown: '未知',
  },
  notificationSwitch: {
    resourceDownload: '资源下载',
    organize: '整理入库',
    subscribe: '订阅',
    site: '站点',
    mediaServer: '媒体服务器',
    manual: '手动处理',
    plugin: '插件',
    other: '其它',
  },
  actionStep: {
    addDownload: '添加下载',
    addSubscribe: '添加订阅',
    fetchDownloads: '获取下载任务',
    fetchMedias: '获取媒体',
    fetchRss: '获取RSS资源',
    fetchTorrents: '获取站点资源',
    filterMedias: '过滤媒体',
    filterTorrents: '过滤资源',
    scanFile: '扫描目录',
    scrapeFile: '刮削文件',
    sendEvent: '发送事件',
    sendMessage: '发送消息',
    transferFile: '整理文件',
    invokePlugin: '调用插件',
    note: '备注',
  },
  qualityOptions: {
    all: '全部',
    blurayOriginal: '蓝光原盘',
    remux: 'Remux',
    bluray: 'BluRay',
    uhd: 'UHD',
    webdl: 'WEB-DL',
    hdtv: 'HDTV',
    h265: 'H265',
    h264: 'H264',
  },
  resolutionOptions: {
    all: '全部',
    '4k': '4k',
    '1080p': '1080p',
    '720p': '720p',
  },
  effectOptions: {
    all: '全部',
    dolbyVision: '杜比视界',
    dolbyAtmos: '杜比全景声',
    hdr: 'HDR',
    sdr: 'SDR',
  },
  theme: {
    light: '浅色',
    dark: '深色',
    auto: '跟随系统',
    transparent: '透明',
    purple: '幻紫',
    custom: '附加样式',
    transparency: '透明度',
    transparencyAdjust: '透明度调整',
    transparencyOpacity: '透明度',
    transparencyBlur: '模糊度',
    transparencyReset: '重置',
    transparencyLow: '低透明度',
    transparencyMedium: '中等透明度',
    transparencyHigh: '高透明度',
    customCssSaveSuccess: '自定义CSS保存成功，请刷新页面生效！',
    customCssSaveFailed: '保存自定义CSS到服务端失败',
    deviceNotSupport: '当前设备不支持监听系统主题变化',
  },
  app: {
    moviepilot: 'MoviePilot',
    slogan: '智能影视媒体库管理工具',
    recommend: '推荐',
    subscribeMovie: '电影订阅',
    subscribeTv: '电视剧订阅',
    settings: '设置',
    selectLanguage: '选择语言',
    logout: '退出登录',
    restarting: '正在重启...',
    confirmRestart: '确认重启系统吗？',
    restartTip: '重启后，您将被注销并需要重新登录。',
    restartTimeout: '重启超时，系统可能需要更长时间恢复，请稍后手动刷新页面',
    restartFailed: '重启失败，请检查系统状态',
    offline: '应用已离线',
    offlineMessage: '网络连接已断开，部分功能可能受限',
    online: '应用在线',
    onlineMessage: '网络连接已恢复',
  },
  pwa: {
    installApp: '安装 MoviePilot 应用',
    installDescription: '获得更好的离线体验和性能',
    install: '安装',
    installSuccess: '应用安装成功！',
    installGuide: '安装指南',
    installInstructions: '在 {platform} 上安装 MoviePilot：',
    installNote: '安装后，您可以从主屏幕快速访问 MoviePilot，并享受离线功能。',
    gotIt: '知道了',
    // 平台特定的说明
    platforms: {
      ios: 'iOS',
      android: 'Android',
      chrome: 'Chrome',
      edge: 'Edge',
      firefox: 'Firefox',
      safari: 'Safari',
      desktop: '桌面设备',
      mobile: '移动设备',
      other: '其他浏览器',
    },
    // 安装步骤
    installSteps: {
      ios: {
        0: '点击浏览器底部的分享按钮',
        1: '选择"添加到主屏幕"',
        2: '点击"添加"确认安装',
      },
      android: {
        0: '点击浏览器菜单（三个点）',
        1: '选择"添加到主屏幕"或"安装应用"',
        2: '点击"安装"确认',
      },
      chrome: {
        0: '点击地址栏右侧的安装图标',
        1: '或者点击浏览器菜单中的"安装 MoviePilot"',
        2: '点击"安装"确认',
      },
      edge: {
        0: '点击地址栏右侧的"应用可用"图标',
        1: '在弹出的面板中点击"安装"按钮',
        2: '在确认对话框中点击"安装"',
      },
      firefox: {
        0: '点击地址栏右侧的安装图标',
        1: '选择"安装"',
        2: '确认安装到桌面',
      },
      safari: {
        0: '点击分享按钮',
        1: '选择"添加到主屏幕"',
        2: '点击"添加"确认',
      },
      desktop: {
        0: '点击地址栏右侧的安装图标',
        1: '选择"安装应用"',
        2: '按照提示完成安装',
      },
      mobile: {
        0: '点击浏览器菜单',
        1: '选择"添加到主屏幕"',
        2: '确认安装',
      },
      other: {
        0: '查找浏览器中的"安装"选项',
        1: '通常在地址栏或菜单中',
        2: '按照提示完成安装',
      },
    },
  },
  login: {
    wallpapers: '壁纸',
    username: '用户名',
    password: '密码',
    otpCode: '双重验证码',
    stayLoggedIn: '保持登录',
    login: '登录',
    networkError: '登录失败，请检查网络连接！',
    authFailure: '登录失败，请检查用户名、密码或双重验证是否正确！',
    permissionDenied: '登录失败，您没有权限访问！',
    noPermission: '登录失败，您没有任何功能权限，请联系管理员！',
    serverError: '登录失败，服务器错误！',
    loginFailed: '登录失败',
    checkCredentials: '请检查用户名、密码或双重验证码是否正确！',
  },
  menu: {
    start: '开始',
    discovery: '发现',
    subscribe: '订阅',
    organize: '整理',
    system: '系统',
  },
  navItems: {
    dashboard: '仪表盘',
    mediaInfo: '媒体库',
    recommend: '推荐',
    site: '站点',
    search: '搜索',
    searchResult: '搜索结果',
    download: '下载',
    movieSubscribe: '电影订阅',
    tvSubscribe: '电视剧订阅',
    history: '历史记录',
    transfer: '整理',
    rename: '重命名',
    statistic: '统计',
    setting: '设置',
    plugin: '插件',
    user: '用户',
    about: '关于',
    explore: '探索',
    movie: '电影',
    tv: '电视剧',
    workflow: '工作流',
    calendar: '日历',
    downloadManager: '下载管理',
    mediaOrganize: '媒体整理',
    fileManager: '文件管理',
    pluginManager: '插件',
    siteManager: '站点管理',
    userManager: '用户管理',
    settings: '设定',
  },
  settingTabs: {
    system: {
      title: '系统',
      description: '基础设置、下载器（Qbittorrent、Transmission）、媒体服务器（Emby、Jellyfin、Plex）',
    },
    directory: {
      title: '存储 & 目录',
      description: '下载目录、媒体库目录、整理、刮削',
    },
    site: {
      title: '站点',
      description: '站点同步、站点数据刷新、站点重置',
    },
    rule: {
      title: '规则',
      description: '自定义规则、优先级规则组、下载规则',
    },
    search: {
      title: '搜索 & 下载',
      description: '搜索数据源（TheMovieDb、豆瓣、Bangumi）、下载任务标签、搜索站点',
    },
    subscribe: {
      title: '订阅',
      description: '订阅站点、订阅模式、订阅规则、洗版规则',
    },
    scheduler: {
      title: '服务',
      description: '定时作业',
    },
    cache: {
      title: '缓存',
      description: '种子缓存、图片文件缓存管理',
    },
    notification: {
      title: '通知',
      description: '通知渠道（微信、Telegram、Slack、SynologyChat、VoceChat、WebPush）、消息发送范围',
    },
    words: {
      title: '词表',
      description: '自定义识别词、自定义制作组/字幕组、自定义占位符、文件整理屏蔽词',
    },
    about: {
      title: '关于',
      description: '软件版本',
    },
  },
  subscribeTabs: {
    movie: {
      mysub: '我的订阅',
      popular: '热门订阅',
    },
    tv: {
      mysub: '我的订阅',
      popular: '热门订阅',
      share: '订阅分享',
    },
  },
  workflowTabs: {
    list: '我的工作流',
    share: '工作流分享',
  },
  pluginTabs: {
    installed: '我的插件',
    market: '插件市场',
  },
  discoverTabs: {
    themoviedb: 'TheMovieDb',
    douban: '豆瓣',
    bangumi: 'Bangumi',
  },
  user: {
    admin: '管理员',
    normal: '普通用户',
    active: '激活',
    inactive: '已停用',
    noEmail: '未设置邮箱',
    movieSubscriptions: '电影订阅',
    tvSubscriptions: '剧集订阅',
    cannotDeleteCurrentUser: '不能删除当前登录用户！',
    confirmDeleteUser: '删除用户 {username} 的所有数据，是否确认？',
    deleteSuccess: '用户删除成功',
    deleteFailed: '用户删除失败！',
    profile: '个人信息',
    systemSettings: '系统设定',
    siteAuth: '用户认证',
    helpDocs: '帮助文档',
    restart: '重启',
    management: '用户管理',
    noUsers: '没有用户',
    clickToAddUser: '点击添加用户卡片添加用户',
    addUser: '添加用户',
    editUser: '编辑用户',
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    role: '角色',
    email: '邮箱',
    enabled: '启用',
    disabled: '禁用',
    status: '状态',
    operations: '操作',
  },
  nav: {
    more: '更多',
  },
  notification: {
    center: '通知中心',
    markRead: '设为已读',
    empty: '暂无通知',
    channel: '通知渠道',
    name: '名称',
    nameHint: '通知渠道名称',
    type: '类型',
    typeHint: '通知渠道类型',
    customTypeHint: '自定义通知类型，用于插件实现场景',
    customTypePlaceholder: 'custom',
    nameRequired: '请输入名称',
    enabled: '启用',
    config: '配置',
    wechat: {
      name: '企业微信',
      corpId: '企业ID',
      corpIdHint: '企业微信后台企业信息中的企业ID',
      appId: '应用 AgentId',
      appIdHint: '企业微信自建应用的AgentId',
      appSecret: '应用 Secret',
      appSecretHint: '企业微信自建应用的Secret',
      proxy: '代理地址',
      proxyHint: '微信消息的转发代理地址，2022年6月20日后创建的自建应用才需要，不使用代理时需要保留默认值',
      token: 'Token',
      tokenHint: '微信企业自建应用->API接收消息配置中的Token',
      encodingAesKey: 'EncodingAESKey',
      encodingAesKeyHint: '微信企业自建应用->API接收消息配置中的EncodingAESKey',
      admins: '管理员白名单',
      adminsHint: '可使用管理菜单及命令的用户ID列表，多个ID使用,分隔',
      adminsPlaceholder: '用户ID列表，多个ID使用,分隔',
    },
    telegram: {
      name: 'Telegram',
      token: 'Bot Token',
      tokenHint: 'Telegram机器人token，格式：123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
      chatId: 'Chat ID',
      chatIdHint: '接受消息通知的用户、群组或频道Chat ID',
      users: '用户白名单',
      usersHint: '可使用Telegram机器人的用户ID清单，多个用户用,分隔，不填写则所有用户都能使用',
      admins: '管理员白名单',
      adminsHint: '可使用管理菜单及命令的用户ID列表，多个ID使用,分隔',
      adminsPlaceholder: '用户ID列表，多个ID使用,分隔',
      usersPlaceholder: '用户ID列表，多个ID使用,分隔',
      apiUrl: '代理API地址',
      apiUrlHint: '自定义代理API地址，格式：https://api.telegram.org',
      apiUrlPlaceholder: 'https://api.telegram.org',
    },
    slack: {
      name: 'Slack',
      oauthToken: 'Slack Bot User OAuth Token',
      oauthTokenHint: 'Slack应用`OAuth & Permissions`页面中的`Bot User OAuth Token`',
      appToken: 'Slack App-Level Token',
      appTokenHint: 'Slack应用`OAuth & Permissions`页面中的`App-Level Token`',
      channel: '频道名称',
      channelHint: '消息发送频道，默认`全体`',
    },
    synologychat: {
      name: 'Synology Chat',
      webhook: '机器人传入URL',
      webhookHint: 'Synology Chat机器人传入URL',
      token: '令牌',
      tokenHint: 'Synology Chat机器人令牌',
    },
    vocechat: {
      name: 'VoceChat',
      host: '地址',
      hostHint: 'VoceChat服务端地址，格式：http(s)://ip:port',
      apiKey: '机器人密钥',
      apiKeyHint: 'VoceChat机器人密钥',
      channelId: '频道ID',
      channelIdHint: 'VoceChat的频道ID，不包含#号',
    },
    webpush: {
      name: 'WebPush',
      username: '登录用户名',
      usernameHint: '只有对应的用户登录后才会推送消息',
    },
  },
  shortcut: {
    title: '捷径',
    recognition: {
      title: '识别',
      subtitle: '名称识别测试',
    },
    rule: {
      title: '规则',
      subtitle: '规则测试',
    },
    log: {
      title: '日志',
      subtitle: '实时日志',
    },
    network: {
      title: '网络',
      subtitle: '网速连通性测试',
    },
    system: {
      title: '系统',
      subtitle: '健康检查',
    },
    message: {
      title: '消息',
      subtitle: '消息中心',
    },
  },
  workflow: {
    components: '动作组件',
    clickToAdd: '点击添加',
    dragToCanvas: '拖动到画布',
    tapComponentHint: '点击组件添加到画布',
    dragComponentHint: '拖动组件到画布',
    task: {
      edit: '编辑任务',
      editFlow: '编辑流程',
      share: '分享',
      continue: '继续执行',
      restart: '重新执行',
      run: '立即执行',
      reset: '重置任务',
      delete: '删除任务',
      confirmDelete: '是否确认删除任务 {name} ?',
      confirmReset: '是否确认重置任务 {name} ?',
      deleteSuccess: '删除任务成功！',
      deleteFailed: '删除任务失败：{message}',
      enableSuccess: '启用任务成功！',
      enableFailed: '启用任务失败：{message}',
      pauseSuccess: '停用任务成功！',
      pauseFailed: '停用任务失败：{message}',
      runSuccess: '任务执行完成！',
      runFailed: '任务执行失败：{message}',
      resetSuccess: '重置任务成功！',
      resetFailed: '重置任务失败：{message}',
      status: {
        success: '成功',
        running: '运行中',
        failed: '失败',
        paused: '暂停',
        waiting: '等待',
      },
      info: {
        trigger: '触发方式',
        timer: '定时',
        status: '状态',
        actionCount: '动作数',
        runCount: '已执行次数',
        progress: '进度',
        error: '错误信息',
        manualTrigger: '手动',
      },
    },
    scanFile: {
      title: '扫描目录',
      subtitle: '扫描目录文件到队列',
      storage: '存储',
      directory: '目录',
    },
    addDownload: {
      title: '添加下载',
      subtitle: '添加资源到下载器',
      downloader: '下载器',
      category: '分类',
      savePath: '保存路径',
      sequential: '顺序下载',
      forceResume: '强制继续',
      firstLastPiece: '优先首尾文件',
      onlyLack: '仅下载缺失资源',
      categoryPlaceholder: '多个使用,分隔',
      savePathPlaceholder: '留空自动',
    },
    addSubscribe: {
      title: '添加订阅',
      subtitle: '添加资源到订阅',
      type: '类型',
      name: '名称',
      season: '季',
      episode: '集',
    },
    fetchMedias: {
      title: '获取媒体',
      subtitle: '从媒体服务器获取媒体信息',
      source: '数据源',
      searchType: '搜索类型',
      type: '类型',
      name: '名称',
      year: '年份',
      ranking: '榜单',
      api: '插件API',
      apiPath: 'API路径',
      selectRanking: '选择榜单',
      tmdbTrending: 'TMDB 流行趋势',
      doubanShowing: '豆瓣正在热映',
      bangumiCalendar: 'Bangumi 每日放送',
      tmdbMovies: 'TMDB 热门电影',
      tmdbTvs: 'TMDB 热门电视剧',
      doubanMovieHot: '豆瓣热门电影',
      doubanTvHot: '豆瓣热门电视剧',
      doubanTvAnimation: '豆瓣热门动漫',
      doubanMovies: '豆瓣最新电影',
      doubanTvs: '豆瓣最新电视剧',
      doubanMovieTop250: '豆瓣电影TOP250',
      doubanTvWeeklyChinese: '豆瓣国产剧集榜',
      doubanTvWeeklyGlobal: '豆瓣全球剧集榜',
    },
    filterMedias: {
      title: '过滤媒体',
      subtitle: '根据条件过滤媒体',
      type: '类型',
      name: '名称',
      year: '年份',
      vote: '评分',
    },
    scrapeFile: {
      title: '刮削文件',
      subtitle: '刮削文件元数据',
    },
    sendEvent: {
      title: '发送事件',
      subtitle: '发送系统事件',
    },
    fetchDownloads: {
      title: '获取下载',
      subtitle: '获取下载器任务',
      loop: '循环执行',
      loopInterval: '循环间隔',
    },
    fetchRss: {
      title: '获取RSS',
      subtitle: '获取RSS订阅',
      url: 'URL',
      userAgent: 'User-Agent',
      timeout: '超时时间',
      matchMedia: '匹配媒体数据',
      useProxy: '使用代理',
    },
    fetchTorrents: {
      title: '获取站点资源',
      subtitle: '获取站点种子列表',
      searchType: '搜索类型',
      searchOptions: {
        name: '名称',
        mediaList: '媒体列表',
      },
      name: '名称',
      year: '年份',
      type: '类型',
      season: '季',
      sites: '站点',
      matchMedia: '匹配媒体数据',
    },
    sendMessage: {
      title: '发送消息',
      subtitle: '发送系统消息',
      channel: '渠道',
      userId: '用户ID',
    },
    transferFile: {
      title: '传输文件',
      subtitle: '传输文件到目标目录',
      source: '源目录',
      sourceOptions: {
        fileList: '文件列表',
        downloads: '下载列表',
      },
    },
    filterTorrents: {
      title: '过滤资源',
      subtitle: '对资源列表数据进行过滤',
      quality: '质量',
      resolution: '分辨率',
      effect: '特效',
      size: '大小范围',
      include: '包含（关键字、正则式）',
      exclude: '排除（关键字、正则式）',
      ruleGroups: '过滤规则组',
    },
    invokePlugin: {
      title: '调用插件',
      subtitle: '调用插件执行特定操作',
      plugin: '插件',
      actionid: '动作ID',
      actionParams: '动作参数',
      loadPluginSettingFailed: '加载插件设置失败',
    },
    note: {
      title: '备注',
      subtitle: '添加流程说明注释',
      content: '备注内容',
      placeholder: '请输入备注内容...',
    },
    title: '工作流',
    share: '工作流分享',
    searchShares: '搜索工作流分享',
    noShareData: '暂无分享的工作流',
    sharer: '分享人',
    trigger: '触发方式',
    timer: '定时器',
    manualTrigger: '手动触发',
    actionCount: '动作数量',
    normalFork: '复用工作流',
    cancelShare: '取消分享',
    cancelSuccess: '取消分享成功',
    cancelFailed: '取消分享失败：{message}',
    usageCount: '复用 {count} 次',
    addSuccess: '复用 {name} 成功！',
    addFailed: '复用 {name} 失败：{message}',
    noWorkflow: '没有工作流',
    noWorkflowDescription: '点击添加按钮创建工作流任务。',
  },
  dashboard: {
    storage: '存储空间',
    mediaStatistic: '媒体统计',
    weeklyOverview: '最近入库',
    realTimeSpeed: '实时速率',
    scheduler: '后台任务',
    cpu: 'CPU',
    memory: '内存',
    network: '网络流量',
    upload: '上行',
    download: '下行',
    library: '我的媒体库',
    playing: '继续观看',
    latest: '最近添加',
    settings: '设置仪表板',
    chooseContent: '选择您想在页面显示的内容',
    adaptiveHeight: '自适应组件高度',
    current: '当前',
    episodes: '剧集',
    users: '用户',
    noSchedulers: '没有后台服务',
    weeklyOverviewDescription: '最近一周入库了 {count} 部影片',
    speed: {
      totalUpload: '总上传量',
      totalDownload: '总下载量',
      freeSpace: '磁盘剩余空间',
    },
    processes: {
      title: '系统进程',
      pid: '进程ID',
      name: '进程名称',
      runtime: '运行时间',
      memory: '内存占用',
    },
    errors: {
      loadMediaServer: '加载媒体服务器设置失败:',
      loadLatest: '加载媒体服务器 "{server}" 的最近入库失败:',
    },
  },
  media: {
    status: {
      inLibrary: '已入库',
      missing: '缺失',
      partiallyMissing: '部分缺失',
      subscribed: '已订阅',
    },
    minutes: '分钟',
    overview: '简介',
    seasons: '季',
    seasonNumber: '第 {number} 季',
    episodeCount: '{count}集',
    actions: {
      searchResource: '搜索资源',
      subscribe: '订阅',
      playOnline: '在线播放',
      playInApp: 'APP播放',
      playInWeb: '网页播放',
    },
    search: {
      byTitle: '标题',
      byImdb: 'IMDB链接',
    },
    info: {
      originalTitle: '原始标题',
      status: '状态',
      releaseDate: '上映日期',
      originalLanguage: '原始语言',
      productionCountries: '出品国家',
      productionCompanies: '制作公司',
      doubanId: '豆瓣ID',
    },
    subscribe: {
      normal: '订阅',
      bestVersion: '洗版订阅',
      addFailed: '添加订阅失败：{reason}！',
      canceled: '已取消订阅！',
      cancelFailed: '取消订阅失败：{reason}！',
    },
    castAndCrew: '演员阵容',
    recommendations: '推荐',
    similar: '类似',
    error: {
      title: '出错啦！',
      noMediaInfo: '未识别到媒体信息。',
    },
    server: {
      plex: 'Plex',
      jellyfin: 'Jellyfin',
      emby: 'Emby',
      appLaunchFailed: 'APP启动失败，正在跳转到网页版',
      appNotInstalled: '未检测到APP，正在跳转到网页版',
      downloadApp: '下载APP',
    },
  },
  subscribe: {
    normalSub: '订阅',
    versionSub: '洗版订阅',
    addSuccess: '添加{name}成功！',
    addFailed: '添加{name}失败：{message}！',
    cancelSuccess: '已取消订阅！',
    cancelFailed: '取消订阅失败：{message}！',
    filterSubscriptions: '筛选订阅',
    name: '名称',
    searchShares: '搜索订阅分享',
    keyword: '关键词',
    noShareData: '未获取到分享订阅数据，未开启数据分享或服务器无法连接。',
    noPopularData: '未获取到热门订阅数据，未开启数据分享或服务器无法连接。',
    noFilterData: '没有筛选到相关内容，请更换筛选条件。',
    noSubscribeData: '请通过搜索添加电影、电视剧订阅。',
    sharer: '分享人',
    follow: '关注',
    unfollow: '取消关注',
    recognitionWords: '识别词',
    cancelShare: '取消分享',
    usageCount: '共 {count} 次复用',
    confirmToggle: '是否{action}订阅 {name}？',
    toggleSuccess: '{name} 已{action}！',
    toggleFailed: '{action}失败：{message}',
    resetConfirm: '重置后 {name} 将恢复初始状态，已下载记录将被清除，未入库的内容将会重新下载，是否确认？',
    resetSuccess: '{name} 重置成功！',
    resetFailed: '{name} 重置失败：{message}',
    shareStatistics: '分享统计',
    shareCount: '个分享',
    totalReuseCount: '次复用',
    ranking: '排名',
    noStatisticsData: '暂无分享统计数据',
    bestVersion: '洗版中',
    completed: '订阅完成',
    subscribing: '订阅中',
    notStarted: '未开始',
    pending: '待定',
    paused: '暂停',
  },
  recommend: {
    all: '全部',
    categoryMovie: '电影',
    categoryTV: '电视剧',
    categoryAnime: '动漫',
    categoryRankings: '榜单',
    trendingNow: '流行趋势',
    nowShowing: '正在热映',
    bangumiDaily: 'Bangumi每日放送',
    tmdbHotMovies: 'TMDB热门电影',
    tmdbHotTVShows: 'TMDB热门电视剧',
    doubanHotMovies: '豆瓣热门电影',
    doubanHotTVShows: '豆瓣热门电视剧',
    doubanHotAnime: '豆瓣热门动漫',
    doubanNewMovies: '豆瓣最新电影',
    doubanNewTVShows: '豆瓣最新电视剧',
    doubanTop250: '豆瓣电影TOP250',
    doubanChineseTVRankings: '豆瓣国产剧集榜',
    doubanGlobalTVRankings: '豆瓣全球剧集榜',
    noCategoryContent: '当前分类下没有可显示的内容',
    configureContent: '设置显示内容',
    customizeContent: '自定义内容',
    selectContentToDisplay: '选择您想在页面显示的内容',
    selectAll: '全选',
    selectNone: '全不选',
  },
  discover: {
    setTabOrder: '设置标签顺序',
    dragToReorder: '拖动对标签页进行排序',
  },
  downloading: {
    noDownloader: '没有下载器',
    configureDownloader: '请先在设置中正确配置并启用下载器。',
    title: '下载',
    noTask: '没有任务',
    noTaskDescription: '正在下载的任务将会显示在这里。',
  },
  resource: {
    searchResults: '资源搜索结果',
    keyword: '关键词',
    title: '标题',
    year: '年份',
    season: '季',
    switchingView: '切换视图',
    backToHome: '返回首页',
    searching: '正在搜索，请稍候...',
    noData: '没有数据',
    noResourceFound: '未搜索到任何资源',
  },
  browse: {
    actor: '演员',
  },
  appcenter: {
    others: '其他',
  },
  notFound: {
    title: '⚠️ 页面不存在',
    description: '您想要访问的页面不存在，请检查地址是否正确。',
    backButton: '返回',
  },
  torrent: {
    selectAll: '全选',
    clear: '清除',
    clearFilters: '清除筛选',
    confirm: '确定',
    resources: '个资源',
    noResults: '没有找到匹配的资源',
    sortDefault: '默认',
    sortSite: '站点',
    sortSize: '大小',
    sortSeeder: '做种数',
    sortPublishTime: '发布时间',
    filterSite: '站点',
    filterSeason: '季',
    filterFreeState: '促销状态',
    filterVideoCode: '视频编码',
    filterEdition: '质量',
    filterResolution: '分辨率',
    filterReleaseGroup: '制作组',
    noMatchingResults: '没有数据',
    allFilters: '综合筛选',
    clearAll: '清除全部',
  },
  calendar: {
    episode: '第{number}集',
  },
  storage: {
    name: '名称',
    type: '类型',
    customTypeHint: '自定义存储类型，用于插件等场景',
    usedPercent: '已使用 {percent}%',
    noConfigNeeded: '此存储类型无需配置参数，请直接配置目录！',
    notConfigured: '未配置',
    local: '本地',
    alipan: '阿里云盘',
    u115: '115网盘',
    rclone: 'RClone',
    alist: 'OpenList',
    smb: 'SMB网络共享',
    custom: '自定义',
  },
  filterRules: {
    specSub: '特效字幕',
    cnSub: '中文字幕',
    cnVoi: '国语配音',
    gz: '官种',
    notCnVoi: '排除: 国语配音',
    hkVoi: '粤语配音',
    notHkVoi: '排除: 粤语配音',
    free: '促销: 免费',
    resolution4k: '分辨率: 4K',
    resolution1080p: '分辨率: 1080P',
    resolution720p: '分辨率: 720P',
    not720p: '排除: 720P',
    qualityBlu: '质量: 蓝光原盘',
    notBlu: '排除: 蓝光原盘',
    qualityBluray: '质量: BLURAY',
    notBluray: '排除: BLURAY',
    qualityUhd: '质量: UHD',
    notUhd: '排除: UHD',
    qualityRemux: '质量: REMUX',
    notRemux: '排除: REMUX',
    qualityWebdl: '质量: WEB-DL',
    notWebdl: '排除: WEB-DL',
    quality60fps: '质量: 60fps',
    not60fps: '排除: 60fps',
    codecH265: '编码: H265',
    notH265: '排除: H265',
    codecH264: '编码: H264',
    notH264: '排除: H264',
    effectDolby: '效果: 杜比视界',
    notDolby: '排除: 杜比视界',
    effectAtmos: '效果: 杜比全景声',
    notAtmos: '排除: 杜比全景声',
    effectHdr: '效果: HDR',
    notHdr: '排除: HDR',
    effectSdr: '效果: SDR',
    notSdr: '排除: SDR',
    effect3d: '效果: 3D',
    not3d: '排除: 3D',
  },
  transferType: {
    copy: '复制',
    move: '移动',
    link: '硬链接',
    softlink: '软链接',
  },
  site: {
    noSites: '没有站点',
    sitesWillBeShownHere: '已添加并支持的站点将会在这里显示。',
    noFilterData: '没有符合条件的站点',
    title: '站点',
    status: {
      enabled: '启用',
      disabled: '停用',
    },
    fields: {
      url: '站点地址',
      priority: '优先级',
      status: '状态',
      rss: 'RSS地址',
      timeout: '超时时间（秒）',
      downloader: '下载器',
      cookie: '站点Cookie',
      userAgent: '站点User-Agent',
      authorization: '请求头（Authorization）',
      apiKey: '令牌（API Key）',
      limitAccess: '限制站点访问频率',
      limitInterval: '单位周期（秒）',
      limitCount: '周期内访问次数',
      limitSeconds: '访问间隔（秒）',
      useProxy: '使用代理访问',
      browserSimulation: '浏览器仿真',
    },
    hints: {
      url: '格式：http://www.example.com/',
      priority: '优先级越小越优先',
      status: '站点启用/停用',
      rss: '订阅模式为`站点RSS`时使用的订阅链接，如未自动获取需手动补充',
      timeout: '站点请求超时时间，为0时不限制',
      downloader: '此站点使用的下载器',
      cookie: '站点请求头中的Cookie信息',
      userAgent: '获取Cookie的浏览器对应的User-Agent',
      authorization: '站点请求头中的Authorization信息，特殊站点需要',
      apiKey: '站点的访问API Key，特殊站点需要',
      limitInterval: '限流控制的单位周期时长',
      limitCount: '单位周期内允许的访问次数',
      limitSeconds: '每次访问需要间隔的最小时间',
      useProxy: '使用代理服务器访问该站点',
      browserSimulation: '使用浏览器模拟真实访问该站点',
    },
    actions: {
      add: '新增站点',
      edit: '编辑站点',
    },
    messages: {
      addSuccess: '新增站点成功',
      addFailed: '新增站点失败',
      updateSuccess: '更新成功',
      updateFailed: '更新失败',
    },
    errors: {
      loadDownloader: '加载下载器设置失败',
    },
    testConnectivity: '测试连通性',
    testing: '测试中 ...',
    testSuccess: '{name} 连通性测试成功，可正常使用！',
    testFailed: '{name} 连通性测试失败：{message}',
    connectionNormal: '连接正常',
    connectionSlow: '连接缓慢',
    connectionFailed: '连接失败',
    connectionUnknown: '连接未知',
    deleteConfirm: '是否确认删除站点？',
    deleteSuccess: '{name} 删除成功！',
    deleteFailed: '{name} 删除失败：{message}',
    browseResources: '浏览资源',
    deleteSite: '删除站点',
    updateCookie: '更新Cookie',
    viewUserData: '查看用户数据',
    statistics: '统计信息',
    totalSites: '总站点数',
    normalSites: '正常站点',
    slowSites: '缓慢站点',
    failedSites: '失败站点',
    averageTime: '平均耗时',
    successRate: '成功率',
    successCount: '成功次数',
    failCount: '失败次数',
    lastAccess: '最后访问',
    timeRecords: '耗时记录',
    recentTimeRecords: '最近耗时记录',
    accessTime: '访问时间',
    responseTime: '响应时间',
    noTimeRecords: '暂无耗时记录',
  },
  message: {
    loadMore: '加载更多',
    noMoreData: '没有更多数据',
  },
  logging: {
    level: '级别',
    time: '时间',
    program: '程序',
    content: '内容',
    refreshing: '正在刷新',
  },
  moduleTest: {
    normal: '正常',
    disabled: '未启用',
    error: '错误',
    checking: '正在检查...',
    complete: '检查完成',
    preparing: '准备检查...',
    totalModules: '总模块数',
    recheck: '重新检查',
  },
  nameTest: {
    recognize: '识别',
    recognizing: '识别中...',
    recognizeAgain: '重新识别',
    title: '标题',
    subtitle: '副标题',
  },
  netTest: {
    notTested: '未测试',
    testing: '测试中...',
    normal: '正常',
  },
  ruleTest: {
    test: '测试',
    testing: '正在测试...',
    testAgain: '重新测试',
    title: '标题',
    subtitle: '副标题',
    ruleGroup: '规则组',
    priority: '优先级：{value}',
    noPriorityRule: '未命中任何优先级规则！',
  },
  setting: {
    about: {
      title: '关于 MoviePilot',
      softwareVersion: '软件版本',
      frontendVersion: '前端版本',
      authVersion: '认证资源版本',
      indexerVersion: '站点资源版本',
      configDir: '配置目录',
      dataDir: '数据目录',
      timezone: '时区',
      latest: '最新',
      supportingSites: '支持站点',
      support: '支援',
      documentation: '文档',
      feedback: '问题反馈',
      channel: '发布频道',
      versions: '软件版本',
      latestVersion: '最新软件版本',
      currentVersion: '当前版本',
      viewChangelog: '查看变更日志',
      changelog: '变更日志',
      dataDirectory: '/moviepilot',
      expand: '展开',
      collapse: '收起',
    },
    system: {
      custom: '自定义',
      basicSettings: '基础设置',
      basicSettingsDesc: '设置服务器的全局功能',
      appDomain: '访问域名',
      appDomainHint: '用于发送通知时，添加快捷跳转地址',
      wallpaper: '背景壁纸',
      wallpaperHint: '选择登陆页面背景来源',
      recognizeSource: '识别数据源',
      recognizeSourceHint: '设置默认媒体信息识别数据源',
      mediaServerSyncInterval: '媒体服务器同步间隔',
      mediaServerSyncIntervalHint: '定时同步媒体服务器数据到本地的时间间隔',
      hours: '小时',
      required: '必选项，请勿留空',
      numbersOnly: '仅支持输入数字，请勿输入其他字符',
      minInterval: '间隔不能小于1个小时',
      apiToken: 'API令牌',
      apiTokenHint: '设置外部请求MoviePilot API时使用的token值',
      apiTokenMinChars: '不能小于16位字符',
      apiTokenRequired: '必填项；请输入API Token',
      apiTokenLength: 'API Token不得低于16位',
      githubToken: 'Github Token',
      githubTokenFormat: 'ghp_**** 或 github_pat_****',
      githubTokenHint: '用于提高插件等访问Github API时的限流阈值',
      ocrHost: '验证码识别服务器',
      ocrHostHint: '用于站点签到、更新站点Cookie等识别验证码',
      advancedSettings: '高级设置',
      advancedSettingsDesc: '系统进阶设置，特殊情况下才需要调整',
      downloaders: '下载器',
      downloadersDesc: '只有默认下载器才会被默认使用。',
      mediaServers: '媒体服务器',
      mediaServersDesc: '所有启用的媒体服务器都会被使用。',
      trimeMedia: '飞牛影视',
      system: '系统',
      media: '媒体',
      network: '网络',
      log: '日志',
      lab: '实验室',
      downloaderSaveSuccess: '下载器设置保存成功',
      downloaderSaveFailed: '下载器设置保存失败！',
      defaultDownloaderNotice: '未设置默认下载器，已将【{name}】作为默认下载器',
      mediaServerSaveSuccess: '媒体服务器设置保存成功',
      mediaServerSaveFailed: '媒体服务器设置保存失败！',
      saveFailed: '设置保存失败：{message}！',
      basicSaveSuccess: '基础设置保存成功',
      advancedSaveSuccess: '高级设置保存成功',
      copySuccess: '已复制到剪贴板！',
      copyFailed: '复制失败：可能是浏览器不支持或被用户阻止！',
      copyError: '复制失败！',
      reloading: '正在应用配置...',
      qbittorrent: 'Qbittorrent',
      transmission: 'Transmission',
      emby: 'Emby',
      jellyfin: 'Jellyfin',
      plex: 'Plex',
      reloadSuccess: '系统配置已生效',
      reloadFailed: '重载系统失败！',
      auxAuthEnable: '用户辅助认证',
      auxAuthEnableHint: '允许外部服务进行登录认证以及自动创建用户',
      globalImageCache: '全局图片缓存',
      globalImageCacheHint: '将媒体图片缓存到本地，提升图片加载速度',
      subscribeStatisticShare: '分享订阅数据',
      subscribeStatisticShareHint: '分享订阅统计数据到热门订阅，供其他MPer参考',
      pluginStatisticShare: '上报插件安装数据',
      pluginStatisticShareHint: '上报插件安装数据给服务器，用于统计展示插件安装情况',
      workflowStatisticShare: '分享工作流数据',
      workflowStatisticShareHint: '分享工作流统计数据到热门工作流，供其他MPer参考',
      bigMemoryMode: '大内存模式',
      bigMemoryModeHint: '使用更大的内存缓存数据，提升系统性能',
      dbWalEnable: 'WAL模式',
      dbWalEnableHint: '可提升读写并发性能，但可能在异常情况下增加数据丢失风险，更改后需重启生效',
      tmdbApiDomain: 'TMDB API服务地址',
      tmdbApiDomainPlaceholder: 'api.themoviedb.org',
      tmdbApiDomainHint: '自定义themoviedb API域名或代理地址',
      tmdbApiDomainRequired: '请输入TMDB API域名',
      tmdbImageDomain: 'TMDB 图片服务地址',
      tmdbImageDomainPlaceholder: 'image.tmdb.org',
      tmdbImageDomainHint: '自定义themoviedb图片服务域名或代理地址',
      tmdbImageDomainRequired: '请输入图片服务域名',
      tmdbLocale: 'TMDB 元数据语言',
      tmdbLocalePlaceholder: 'zh',
      tmdbLocaleHint: '自定义themoviedb元数据语言',
      metaCacheExpire: '媒体元数据缓存过期时间',
      metaCacheExpireHint: '识别元数据本地缓存时间，为 0 时使用内置默认值',
      metaCacheExpireRequired: '请输入元数据缓存时间',
      metaCacheExpireMin: '元数据缓存时间必须大于等于0',
      scrapFollowTmdb: '跟随TMDB识别整理',
      scrapFollowTmdbHint: '关闭时以整理历史记录为准（如有），避免TMDB数据在订阅中途修改',
      scrapOriginalImage: 'TMDB 刮削原语种图片',
      scrapOriginalImageHint: '刮削原语种图片，否则刮削元数据语种图片',
      fanartEnable: 'Fanart图片数据源',
      fanartEnableHint: '使用 fanart.tv 的图片数据',
      fanartLang: 'Fanart语言',
      fanartLangHint: '设置Fanart图片的语言偏好，多选时按优先级顺序排列',
      githubProxy: 'Github加速代理',
      githubProxyPlaceholder: '留空表示不使用代理',
      githubProxyHint: '使用代理加速Github访问速度',
      pipProxy: 'PIP加速代理',
      pipProxyPlaceholder: '留空表示不使用代理',
      pipProxyHint: '使用代理加速插件等pip库安装速度',
      dohEnable: 'DNS Over HTTPS',
      dohEnableHint: '使用DOH对特定域名进行解析，以防止DNS污染',
      dohResolvers: 'DOH 服务器',
      dohResolversPlaceholder: 'https://dns.google/dns-query,*******',
      dohResolversHint: 'DNS解析服务器地址，多个地址使用逗号分隔',
      dohDomains: 'DOH 域名',
      dohDomainsPlaceholder: 'example.com,example2.com',
      dohDomainsHint: '使用DOH解析的域名，多个域名使用逗号分隔',
      debug: '调试模式',
      debugHint: '启用调试模式后，日志将以DEBUG级别记录，以便排查问题',
      logLevel: '日志等级',
      logLevelHint: '设置日志记录的级别，用于控制日志输出量',
      logMaxFileSize: '日志文件最大容量(MB)',
      logMaxFileSizeHint: '限制单个日志文件的最大容量，超出后将自动分割日志',
      logMaxFileSizeRequired: '日志文件最大大小',
      logMaxFileSizeMin: '日志文件最大容量必须大于等于1',
      logBackupCount: '日志文件最大备份数量',
      logBackupCountHint: '设置每个模块日志文件的最大备份数量，超过后将覆盖旧日志',
      logBackupCountRequired: '请输入日志文件最大备份数量',
      logBackupCountMin: '日志文件最大备份数量必须大于等于1',
      logFileFormat: '日志文件格式',
      logFileFormatHint: '设置日志文件的输出格式，用于自定义日志的显示内容',
      pluginAutoReload: '插件热加载',
      pluginAutoReloadHint: '修改插件文件后自动重新加载，开发插件时使用',
      encodingDetectionPerformanceMode: '编码探测性能模式',
      encodingDetectionPerformanceModeHint: '优先提升探测效率，但可能降低编码探测的准确性',
      tokenizedSearch: '分词搜索',
      tokenizedSearchHint: '提升整理历史记录搜索精度，但可能增加性能开销和意外结果',
      tmdbLanguage: {
        zhCN: '简体中文',
        zhTW: '繁体中文',
        en: '英文',
      },
      fanartLanguage: {
        zh: '中文',
        en: '英文',
        ja: '日文',
        ko: '韩文',
        de: '德文',
        fr: '法文',
        es: '西班牙文',
        it: '意大利文',
        pt: '葡萄牙文',
        ru: '俄文',
      },
      logLevelItems: {
        debug: 'DEBUG - 调试',
        info: 'INFO - 信息',
        warning: 'WARNING - 警告',
        error: 'ERROR - 错误',
        critical: 'CRITICAL - 严重',
      },
      wallpaperItems: {
        tmdb: 'TMDB电影海报',
        bing: 'Bing每日壁纸',
        mediaserver: '媒体服务器',
        none: '无壁纸',
        customize: '自定义',
      },
      mb: 'MB',
      hour: '小时',
      customizeWallpaperApi: '自定义壁纸API地址',
      customizeWallpaperApiHint: '会获取API返回内容中所有允许的安全域名地址的图片，需要同步设置安全域名地址',
      customizeWallpaperApiRequired: '必填项；请输入自定义壁纸API',
      securityImageDomains: '安全图片域名',
      securityImageDomainsHint: '允许缓存的图片域名白名单，用于控制可信任的图片来源',
      noSecurityImageDomains: '暂无安全域名',
      securityImageDomainAdd: '添加域名，如：image.tmdb.org',
      proxyHost: '代理服务器',
      proxyHostHint: '设置代理服务器地址，支持：http(s)、socks5、socks5h 等协议',
      moviePilotAutoUpdate: '自动更新MoviePilot',
      moviePilotAutoUpdateHint: '重启时自动更新MoviePilot到最新发行版本',
      autoUpdateResource: '自动更新站点资源',
      autoUpdateResourceHint: '重启时自动检测和更新站点资源包',
      // 刮削开关设置
      scrapingSwitchSettings: '刮削开关设置',
      scrapingSwitchSettingsDesc: '控制各类媒体文件的刮削功能开关',
      movie: '电影',
      tv: '电视剧',
      season: '季',
      episode: '集',
      movieNfo: 'NFO',
      moviePoster: '海报',
      movieBackdrop: '背景图',
      movieLogo: 'Logo',
      movieDisc: '光盘图',
      movieBanner: '横幅图',
      movieThumb: '缩略图',
      tvNfo: 'NFO',
      seasonNfo: 'NFO',
      tvPoster: '海报',
      tvBackdrop: '背景图',
      tvBanner: '横幅图',
      tvLogo: 'Logo',
      tvThumb: '缩略图',
      seasonPoster: '海报',
      seasonBanner: '横幅图',
      seasonThumb: '缩略图',
      episodeNfo: 'NFO',
      episodeThumb: '缩略图',
      scrapingSwitchSaveFailed: '刮削开关设置保存失败：{message}',
      scrapingSwitchSaveError: '刮削开关设置保存失败',
    },
    site: {
      siteSync: '站点同步',
      siteSyncDesc: '从CookieCloud快速同步站点数据',
      enableLocalCookieCloud: '启用本地CookieCloud服务器',
      enableLocalCookieCloudHint: '使用内建CookieCloud服务同步站点数据，服务地址为：http://localhost:3000/cookiecloud',
      serviceAddress: '服务地址',
      serviceAddressPlaceholder: 'https://movie-pilot.org/cookiecloud',
      serviceAddressHint: '远端CookieCloud服务地址，格式：https://movie-pilot.org/cookiecloud',
      userKey: '用户KEY',
      userKeyHint: 'CookieCloud浏览器插件生成的用户KEY',
      e2ePassword: '端对端加密密码',
      e2ePasswordHint: 'CookieCloud浏览器插件生成的端对端加密密码',
      autoSyncInterval: '自动同步间隔',
      autoSyncIntervalHint: '从CookieCloud服务器自动同步站点Cookie到MoviePilot的时间间隔',
      syncBlacklist: '同步域名黑名单',
      syncBlacklistPlaceholder: '多个域名,分割',
      syncBlacklistHint: 'CookieCloud同步域名黑名单，多个域名,分割',
      userAgent: '浏览器User-Agent',
      userAgentHint: 'CookieCloud插件所在的浏览器的User-Agent',
      siteDataRefresh: '站点数据刷新',
      siteDataRefreshInterval: '站点数据刷新间隔',
      siteDataRefreshIntervalHint: '刷新站点用户上传下载等数据的时间间隔',
      readSiteMessage: '阅读站点消息',
      readSiteMessageHint: '刷新数据时读取站点消息并发送通知',
      siteReset: '站点重置',
      confirmReset: '确认删除所有站点数据并重新同步。',
      confirmResetHint: '删除所有站点数据并重新从CookieCloud同步，操作请先清空涉及站点的相关设置。',
      resetSites: '重置站点数据',
      resettingSites: '正在重置...',
      syncInterval: {
        hourly: '每小时',
        every6Hours: '每6小时',
        every12Hours: '每12小时',
        daily: '每天',
        weekly: '每周',
        monthly: '每月',
        never: '永不',
      },
      saveSuccess: '保存站点设置成功',
      saveFailed: '站点设置保存失败！',
      resetSuccess: '站点重置成功，请等待CookieCloud同步完成！',
      resetFailed: '站点重置失败！',
    },
    notification: {
      channels: '通知渠道',
      channelsDesc: '设置消息发送渠道参数',
      organizeSuccess: '资源入库',
      downloadAdded: '资源下载',
      subscribeAdded: '添加订阅',
      subscribeComplete: '订阅完成',
      templateConfigTitle: '通知模板',
      templateConfigDesc: '设置通知模板，支持Jinja2语法。',
      templateSaveFailed: '模板保存失败！',
      templateSaveSuccess: '模板保存成功',
      templateLoadFailed: '模板加载失败！',
      scope: '通知发送范围',
      scopeDesc: '对应消息类型只会发送给设定的用户。',
      messageType: '消息类型',
      scopeRange: '范围',
      operationUserOnly: '仅操作用户',
      adminOnly: '仅管理员',
      userAndAdmin: '操作用户和管理员',
      allUsers: '所有用户',
      sendTime: '通知发送时间',
      sendTimeDesc: '设定消息发送的时间范围。',
      startTime: '开始时间',
      endTime: '结束时间',
      saveSuccess: '通知设置保存成功',
      saveFailed: '通知设置保存失败！',
      switchSaveSuccess: '消息类型开关保存成功',
      switchSaveFailed: '消息类型开关保存失败！',
      timeSaveSuccess: '通知发送时间保存成功',
      timeSaveFailed: '通知发送时间保存失败！',
      channel: '通知',
      wechat: '微信',
      resourceDownload: '资源下载',
      mediaImport: '整理入库',
      subscription: '订阅',
      site: '站点',
      mediaServer: '媒体服务器',
      manualProcess: '手动处理',
      plugin: '插件',
      other: '其它',
      telegram: 'Telegram',
      slack: 'Slack',
      synologyChat: 'SynologyChat',
      voceChat: 'VoceChat',
      webPush: 'WebPush',
      custom: '自定义通知',
    },
    words: {
      customIdentifiers: '自定义识别词',
      identifiersDesc: '添加规则对种子名或者文件名进行预处理以校正识别',
      identifiersPlaceholder: '支持正则表达式，特殊字符需要\\转义，一行为一组',
      identifiersHint: '支持正则表达式，特殊字符需要\\转义，一行为一组',
      formatTitle: '支持的配置格式（注意空格）：',
      formatContent:
        '屏蔽词\n' +
        '被替换词 => 替换词\n' +
        '前定位词 <> 后定位词 >> 集偏移量（EP）\n' +
        '被替换词 => 替换词 && 前定位词 <> 后定位词 >> 集偏移量（EP）\n' +
        '其中替换词支持格式：&#123;[tmdbid/doubanid=xxx;type=movie/tv;s=xxx;e=xxx]&#125; 直接指定TMDBID/豆瓣ID识别，其中s、e为季数和集数（可选）',
      identifierSaveSuccess: '自定义识别词保存成功',
      identifierSaveFailed: '自定义识别词保存失败！',

      customReleaseGroups: '自定义制作组/字幕组',
      releaseGroupsDesc: '添加无法识别的制作组/字幕组。',
      releaseGroupsPlaceholder: '支持正则表达式，特殊字符需要\\转义，一行代表一个制作组/字幕组',
      releaseGroupsHint: '支持正则表达式，特殊字符需要\\转义，一行代表一个制作组/字幕组',
      releaseGroupSaveSuccess: '自定义制作组/字幕组保存成功',
      releaseGroupSaveFailed: '自定义制作组/字幕组保存失败！',

      customization: '自定义占位符',
      customizationDesc: '添加自定义占位符识别正则，重命名格式中添加{customization}使用。',
      customizationPlaceholder: '支持正则表达式，特殊字符需要\\转义，多个匹配对象请换行分隔',
      customizationHint: '支持正则表达式，特殊字符需要\\转义，多个匹配对象请换行分隔',
      customizationSaveSuccess: '自定义占位符保存成功',
      customizationSaveFailed: '自定义占位符保存失败！',

      transferExcludeWords: '文件整理屏蔽词',
      excludeWordsDesc: '目录名或文件名中包含屏蔽词时不进行整理。',
      excludeWordsPlaceholder: '支持正则表达式，特殊字符需要\\转义，一行代表一个屏蔽词',
      excludeWordsHint: '支持正则表达式，特殊字符需要\\转义，一行代表一个屏蔽词',
      excludeWordsSaveSuccess: '文件整理屏蔽词保存成功',
      excludeWordsSaveFailed: '文件整理屏蔽词保存失败！',
    },
    search: {
      basicSettings: '基础设置',
      basicSettingsDesc: '设定数据源、规则组等基础信息',
      recognizeSource: '识别数据源',
      recognizeSourceDesc: '默认使用TMDB。豆瓣识别中文作品通常更友好，但有些国外作品信息不完整。',
      themoviedb: 'TheMovieDb',
      douban: '豆瓣',
      filterRuleGroup: '过滤规则组',
      filterRuleGroupDesc: '设置下载过程中使用的过滤规则组。',
      downloadLabel: '下载任务标签',
      downloadLabelDesc: '下载器中的下载标签，用于过滤查询。',
      downloadLabelHint: '支持增加多个标签，英文逗号分隔',
      downloadSite: '搜索站点',
      downloadSiteDesc: '设置指定分类搜索的站点范围。',
      movieSites: '电影站点',
      tvSites: '电视剧站点',
      animeSites: '动漫站点',
      saveSites: '保存站点',
      saveSuccess: '保存搜索设置成功',
      saveFailed: '搜索设置保存失败！',
      saveRuleFailed: '规则保存失败！',
      movieCategory: '电影',
      tvCategory: '电视剧',
      animeCategory: '动漫',
      downloadUser: '远程搜索自动下载用户',
      downloadUserHint: '使用Telegram、微信等搜索时是否自动下载，使用逗号分割，设置为 all 代表所有用户自动择优下载',
      multipleNameSearch: '多名称资源搜索',
      multipleNameSearchHint: '使用多个名称（中文、英文等）搜索站点资源并合并搜索结果，会增加站点访问频率',
      downloadSubtitle: '下载站点字幕',
      downloadSubtitleHint: '检查站点资源是否有单独的字幕文件并自动下载',
      mediaSource: '媒体搜索数据源',
      mediaSourceHint: '搜索媒体信息时使用的数据源以及排序',
      filterRuleGroupHint: '搜索媒体信息时按选定的过滤规则组对结果进行过滤',
      downloadUserPlaceholder: '用户ID1,用户ID2',
      downloadLabelPlaceholder: 'MOVIEPILOT',
    },
    directory: {
      storage: '存储',
      storageDesc: '设置本地或网盘存储',
      directory: '目录',
      mediaType: '媒体类型',
      directoryDesc: '设置媒体文件整理目录结构，按先后顺序依次匹配。',
      organizeAndScrap: '整理 & 刮削',
      organizeAndScrapDesc: '设置重命名格式、刮削选项等。',
      scrapSource: '刮削数据源',
      scrapSourceHint: '刮削时的元数据来源',
      movieRenameFormat: '电影重命名格式',
      movieRenameFormatHint: '使用Jinja2语法，格式参考：https://jinja.palletsprojects.com/en/3.0.x/templates',
      tvRenameFormat: '电视剧重命名格式',
      tvRenameFormatHint: '使用Jinja2语法，格式参考：https://jinja.palletsprojects.com/en/3.0.x/templates',
      saveSuccess: '存储设置保存成功',
      saveFailed: '存储设置保存失败！',
      directorySaveSuccess: '目录设置保存成功',
      directorySaveFailed: '目录设置保存失败！',
      organizeSaveSuccess: '整理选项设置保存成功',
      organizeSaveFailed: '整理选项设置保存失败！',
      duplicateDirectoryName: '存在重复目录名称！无法保存，请修改！',
      defaultDirName: '目录',
      storageSaveSuccess: '存储设置保存成功',
      storageSaveFailed: '存储设置保存失败！',
    },
    rule: {
      customRules: '自定义规则',
      customRulesDesc: '自定义优先级规则项',
      priorityRuleGroups: '优先级规则组',
      priorityRuleGroupsDesc: '预设优先级规则组，以便在搜索和订阅中使用。',
      downloadRules: '下载规则',
      downloadRulesDesc: '同时命中多个资源时择优下载。',
      resourcePriority: '资源优先级',
      sitePriority: '站点优先级',
      siteUpload: '站点上传量',
      resourceSeeder: '资源做种数',
      emptyIdError: '存在空ID的规则，无法保存，请修改！',
      emptyNameError: '存在空名字的规则，无法保存，请修改！',
      duplicateIdError: '存在重复规则ID！无法保存，请修改！',
      duplicateNameError: '存在重复规则名称！无法保存，请修改！',
      customRuleSaveSuccess: '自定义规则保存成功',
      customRuleSaveFailed: '自定义规则保存失败！',
      emptyGroupNameError: '存在空名字的规则组！无法保存，请修改！',
      duplicateGroupNameError: '存在重复规则组名称！无法保存，请修改！',
      ruleGroupSaveSuccess: '优先级规则组保存成功',
      ruleGroupSaveFailed: '优先级规则组保存失败！',
      customRuleCopySuccess: '自定义规则已复制到剪贴板！',
      customRuleCopyFailed: '自定义规则复制失败：可能是浏览器不支持或被用户阻止！',
      customRuleCopyError: '自定义规则复制失败！',
      ruleGroupCopySuccess: '优先级规则组已复制到剪贴板！',
      ruleGroupCopyFailed: '优先级规则组复制失败：可能是浏览器不支持或被用户阻止！',
      ruleGroupCopyError: '优先级规则组复制失败！',
      currentPriorityRules: '当前使用下载优先规则',
      currentPriorityRulesHint: '排在前面的优先级越高，未选择的项不纳入排序',
      importCustomRules: '导入自定义规则',
      importRuleGroups: '导入优先级规则组',
      importFailed: '导入规则失败！无法解析输入的数据！',
      importUnknownType: '导入规则失败！未知的数据类型！',
      duplicateValue: '存在重名值',
      importNoId: '导入失败！发现有规则不存在ID，可能属于优先级规则组！',
      importHasId: '导入失败！发现有规则存在相同ID，可能属于自定义规则！',
    },
    scheduler: {
      title: '定时作业',
      subtitle: '包含系统内置服务以及插件提供的服务',
      provider: '提供者',
      taskName: '任务名称',
      taskStatus: '任务状态',
      nextRunTime: '下一次执行时间',
      execute: '执行',
      noService: '没有后台服务',
      running: '正在运行',
      stopped: '已停止',
      waiting: '等待',
      executeSuccess: '定时作业执行请求提交成功！',
    },
    subscribe: {
      basicSettings: '基础设置',
      basicSettingsDesc: '设定订阅模式、周期等基础设置',
      subscribeSites: '订阅站点',
      subscribeSitesDesc: '只有选中的站点才会在订阅中使用。',
      mode: '订阅模式',
      modeHint: '自动：自动爬取站点首页，站点RSS：通过站点RSS链接订阅',
      rssInterval: '站点RSS周期',
      rssIntervalHint: '设置站点RSS运行周期，在订阅模式为`站点RSS`时生效',
      filterRuleGroup: '订阅优先级规则组',
      filterRuleGroupHint: '按选定的过滤规则组对订阅进行过滤',
      bestVersionRuleGroup: '洗版优先级规则组',
      bestVersionRuleGroupHint: '按选定的过滤规则组对洗版订阅进行过滤',
      timedSearch: '订阅定时搜索',
      timedSearchHint: '每隔24小时全站搜索，以补全订阅可能漏掉的资源',
      checkLocalMedia: '检查文件系统资源',
      checkLocalMediaHint: '扫描存储目录中是否已存在相应资源文件，以避免重复下载；不管是否开启都会检查媒体服务器',
      modes: {
        auto: '自动',
        rss: '站点RSS',
      },
      intervals: {
        min5: '5分钟',
        min10: '10分钟',
        min20: '20分钟',
        min30: '半小时',
        hour1: '1小时',
        hour12: '12小时',
        day1: '1天',
      },
      saveSuccess: '订阅站点保存成功',
      saveFailed: '订阅站点保存失败！',
      settingsSaveSuccess: '订阅基础设置保存成功',
      settingsSaveFailed: '订阅基础设置保存失败！',
    },
    cache: {
      title: '缓存管理',
      subtitle: '管理缓存的站点资源',
      filterByTitle: '按标题筛选',
      filterBySite: '按站点筛选',
      selectSite: '选择站点',
      refresh: '刷新缓存',
      deleteSelected: '删除选中',
      clearAll: '清空缓存',
      refreshSuccess: '缓存刷新完成',
      refreshFailed: '刷新缓存失败',
      clearSuccess: '缓存清理完成',
      clearFailed: '清理缓存失败',
      deleteSuccess: '缓存项删除成功',
      deleteFailed: '删除缓存项失败',
      deleteSelectedSuccess: '成功删除 {count} 个缓存项',
      deleteSelectedFailed: '删除缓存项失败',
      loadFailed: '加载缓存数据失败',
      selectDeleteWarning: '请选择要删除的缓存项',
      reidentify: '重新识别',
      reidentifySuccess: '重新识别完成',
      reidentifyFailed: '重新识别失败',
      poster: '海报',
      torrentTitle: '标题',
      site: '站点',
      size: '大小',
      publishTime: '发布时间',
      recognitionResult: '识别结果',
      actions: '操作',
      unrecognized: '未识别',
      noData: '暂无缓存数据',
      noDataHint: '点击"刷新缓存"按钮获取最新的种子缓存',
      reidentifyDialog: {
        title: '重新识别',
        torrentInfo: '种子信息',
        tmdbId: 'TMDB ID',
        tmdbIdHint: '可选，手动指定TMDB ID进行识别',
        doubanId: '豆瓣 ID',
        doubanIdHint: '可选，手动指定豆瓣ID进行识别',
        autoHint: '如果不指定ID，将自动重新识别该种子',
        cancel: '取消',
        confirm: '重新识别',
      },
      mediaType: {
        movie: '电影',
        tv: '电视剧',
      },
      clearConfirm: '确认清空所有缓存吗？',
    },
  },
  dialog: {
    progress: {
      processing: '处理中',
    },
    subscribeSeason: {
      title: '订阅 - {title}',
      selectGroup: '选择剧集组',
      defaultGroup: '默认',
      seasonCount: '{count} 季',
      episodeCount: '{count} 集',
      seasonNumber: '第 {number} 季',
      airDate: '首播于 {date}',
      voteAverage: '{score}',
      status: {
        exists: '已入库',
        partial: '部分缺失',
        missing: '缺失',
      },
      submit: '提交订阅',
      selectSeasons: '请选择订阅季',
    },
    userAddEdit: {
      add: '添加用户',
      edit: '编辑用户',
      username: '用户名',
      password: '密码',
      confirmPassword: '确认密码',
      email: '邮箱',
      nickname: '昵称',
      status: '状态',
      active: '激活',
      inactive: '已停用',
      superUser: '超级用户',
      otp: '启用二次验证',
      avatar: '头像',
      uploadAvatar: '上传头像',
      resetDefaultAvatar: '重置默认头像',
      restoreCurrentAvatar: '还原当前头像',
      notifications: '通知',
      wechat: '微信ID',
      telegram: 'Telegram ID',
      slack: 'Slack ID',
      vocechat: 'VoceChat ID',
      synologyChat: 'SynologyChat ID',
      webPush: 'WebPush',
      creatingUser: '正在创建【{name}】用户，请稍后',
      updatingUser: '正在更新【{name}】用户，请稍后',
      usernameRequired: '用户名不能为空',
      usernameExists: '用户名已存在',
      passwordMismatch: '两次输入的密码不一致',
      userCreated: '用户【{name}】创建成功',
      userCreateFailed: '创建用户失败：{message}',
      userUpdateSuccess: '用户【{name}】更新成功',
      userUpdateFailed: '更新用户失败：{message}',
      userDeleteSuccess: '用户【{name}】删除成功',
      userDeleteFailed: '删除用户失败：{message}',
      invalidFile: '上传的文件不符合要求，请重新选择头像',
      fileSizeLimit: '文件大小不得大于800KB',
      avatarUploadSuccess: '新头像上传成功，待保存后生效!',
      resetAvatarSuccess: '已重置为默认头像，待保存后生效！',
      restoreAvatarSuccess: '已还原当前使用头像！',
      deleteConfirm: '确认删除用户【{name}】吗？',
      saveUserInfo: '保存用户信息',
      cannotDeleteCurrentUser: '不能删除当前登录用户',
      deleteUser: '删除用户',
      permissions: {
        title: '权限设置',
        presetNormal: '普通用户',
        presetAdmin: '管理员',
        discovery: '发现',
        discoveryDesc: '访问推荐和探索功能',
        search: '搜索',
        searchDesc: '搜索站点资源和添加下载',
        subscribe: '订阅',
        subscribeDesc: '管理电影和电视剧订阅',
        manage: '管理',
        manageDesc: '访问下载管理和站点管理等功能',
      },
    },
    searchBar: {
      search: '搜索',
      searchPlaceholder: '搜索功能、订阅、设置...',
      recentSearches: '最近搜索',
      noRecentSearches: '没有最近搜索记录',
      functions: '功能',
      noFunctionsFound: '没有匹配的功能',
      plugins: '插件',
      noPluginsFound: '没有匹配的插件',
      subscriptions: '订阅',
      noSubscriptionsFound: '没有匹配的订阅',
      searchSites: '搜索站点',
      selectSites: '选择站点',
      collections: '系列合集',
      collectionSearch: '相关的系列作品',
      actorSearch: '相关的演员、导演等',
      historySearch: '相关的历史记录',
      subscribeShareSearch: '相关的订阅分享',
      siteResources: '站点资源',
      searchInSites: '在站点中搜索种子资源',
      relatedResources: '相关资源',
      searchTip: '可搜索电影、电视剧、演员、资源等',
    },
    searchSite: {
      selectSites: '选择站点',
      siteSearch: '站点搜索',
      searchAllSites: '已选择 {selected}/{total} 个站点',
      selectAll: '选择全部',
      deselectAll: '取消全选',
      confirm: '确认',
      cancel: '取消',
    },
    importCode: {
      import: '导入',
      title: '导入代码',
    },
    addDownload: {
      confirmDownload: '确认下载',
      downloader: '下载器（默认）',
      saveDirectory: '保存目录（自动）',
      defaultPlaceholder: '留空默认',
      autoPlaceholder: '留空自动匹配',
      downloading: '下载中...',
      startDownload: '开始下载',
      downloadSuccess: '{site} {title} 下载成功！',
      downloadFailed: '{site} {title} 下载失败：{message}！',
    },
    subscribeShare: {
      shareSubscription: '分享订阅',
      season: '第 {number} 季',
      title: '标题',
      description: '说明',
      descriptionHint: '填写关于该订阅的说明，订阅中的搜索词、识别词等将会默认包含在分享中',
      shareUser: '分享用户',
      shareUserHint: '分享人的昵称',
      confirmShare: '确认分享',
      shareSuccess: '{name} 分享成功！',
      shareFailed: '{name} 分享失败：{message}！',
    },
    workflowShare: {
      shareWorkflow: '分享工作流',
      title: '标题',
      description: '说明',
      descriptionHint: '填写关于该工作流的说明，工作流的动作和流程将会默认包含在分享中',
      shareUser: '分享用户',
      shareUserHint: '分享人的昵称',
      confirmShare: '确认分享',
      shareSuccess: '{name} 分享成功！',
      shareFailed: '{name} 分享失败：{message}！',
      securityWarning: '安全提醒',
      securityWarningMessage: '分享前请确保工作流没有敏感信息，比如RSS链接中的PassKey等，避免产生信息泄露。',
    },
    u115Auth: {
      loginTitle: '115网盘登录',
      scanQrCode: '请使用微信或115客户端扫码',
      scanned: '已扫码，请确认登录',
      complete: '完成',
      reset: '重置',
    },
    aliyunAuth: {
      loginTitle: '阿里云盘登录',
      scanQrCode: '请用阿里云盘 App 扫码',
      scanned: '已扫码',
      complete: '完成',
      reset: '重置',
    },
    rcloneConfig: {
      title: 'RClone配置',
      filePath: 'rclone配置文件路径',
      fileContent: 'rclone配置文件内容',
      defaultContent: '# 请在此处填写rclone配置文件内容 \n# 请参考 https://rclone.org/docs/ \n# 存储节点名必须为：MP',
      complete: '完成',
      reset: '重置',
    },
    alistConfig: {
      title: 'OpenList配置',
      serverUrl: 'OpenList服务地址',
      username: '用户名',
      password: '密码',
      tokenUrl: '获取Token地址',
      loginType: '登录方式',
      loginTypeOptions: {
        guest: '访客',
        username: '用户名密码',
        token: '令牌',
      },
      complete: '完成',
      reset: '重置',
    },
    smbConfig: {
      title: 'SMB网络共享配置',
      host: 'SMB服务器地址',
      hostHint: 'SMB服务器的IP地址或主机名',
      share: '共享名称',
      shareHint: '要连接的共享文件夹名称',
      username: '用户名',
      usernameHint: 'SMB登录用户名',
      password: '密码',
      passwordHint: 'SMB登录密码',
      domain: '域名',
      domainHint: 'SMB域名，如WORKGROUP或域控制器名称',
      complete: '完成',
      reset: '重置',
    },
    workflowAddEdit: {
      addTitle: '添加工作流',
      editTitle: '编辑工作流',
      name: '名称',
      namePlaceholder: '工作流名称',
      desc: '描述',
      descPlaceholder: '工作流描述',
      enabled: '启用',
      triggerType: '触发类型',
      triggerTypeTimer: '定时触发',
      triggerTypeEvent: '事件触发',
      triggerTypeManual: '手动触发',
      schedule: '定时执行',
      cronExpr: 'Cron表达式',
      cronExprDesc: '工作流定时执行的cron表达式',
      eventType: '事件类型',
      eventTypePlaceholder: '请选择事件类型',
      nameRequired: '请填写完整信息！',
      triggerRequired: '请选择触发类型！',
      timerRequired: '请填写定时表达式！',
      eventTypeRequired: '请选择事件类型！',
      addSuccess: '创建任务成功，请编辑流程！',
      addFailed: '创建任务失败：{message}',
      editSuccess: '修改任务成功！',
      editFailed: '修改任务失败：{message}',
      cancel: '取消',
      confirm: '确认',
    },
    workflowActions: {
      title: '编辑流程',
      noActionsMessage: '工作流没有动作，请添加动作',
      addAction: '添加动作',
      editAction: '编辑动作',
      deleteAction: '删除动作',
      moveUp: '上移',
      moveDown: '下移',
      nameLabel: '动作名称',
      nameRequired: '动作名称不能为空',
      typeLabel: '动作类型',
      typeRequired: '动作类型不能为空',
      paramsLabel: '动作参数',
      outputLabel: '动作输出',
      saveAction: '保存动作',
      cancelAction: '取消',
      confirmDeleteTitle: '确认删除动作',
      confirmDeleteMessage: '确定要删除此动作吗？此操作无法撤销。',
      yesDelete: '是的，删除',
      noCancel: '取消',
      invalidConnection: '非法连接：不能连接自身或同类型端口！',
      componentNotFound: '组件 {component} 未找到',
      componentAdded: '已添加组件到画布',
      saveSuccess: '保存任务流程成功！',
      saveFailed: '保存任务流程失败：{message}',
      importTitle: '导入任务流程',
      importSuccess: '导入成功！',
      importFailed: '导入失败！',
      codeCopied: '任务流程代码已复制到剪贴板！',
    },
    siteCookieUpdate: {
      title: '更新站点Cookie & UA',
      processing: '请稍候...',
      updating: '正在更新 {site} Cookie & UA...',
      success: '{site} 更新Cookie & UA成功！',
      failed: '{site} 更新失败：{message}',
      updateButton: '开始更新',
    },
    siteAddEdit: {
      addTitle: '添加站点',
      editTitle: '编辑站点',
      nameLabel: '站点名称',
      urlLabel: '站点URL',
      iconLabel: '站点图标',
      uploadIcon: '上传图标',
      cookie: 'Cookie',
      rssUrl: 'RSS链接',
      enableLabel: '启用',
      pubEnableLabel: '资源公开',
      priorityLabel: '优先级',
      signInLabel: '签到',
      proxies: '代理',
      userInfo: '用户信息',
      cancel: '取消',
      confirm: '保存',
    },
    pluginConfig: {
      title: '插件配置',
      save: '保存',
      close: '关闭',
      viewData: '查看数据',
      saving: '正在保存 {name} 配置...',
      saveSuccess: '插件 {name} 配置已保存',
      saveFailed: '插件 {name} 配置保存失败：{message}',
    },
    pluginData: {
      title: '插件数据',
      save: '保存',
      close: '关闭',
    },
    pluginMarketSetting: {
      title: '插件市场设置',
      repoUrl: '插件仓库地址',
      repoPlaceholder: '格式：https://github.com/jxxghp/MoviePilot-Plugins/,https://github.com/xxxx/xxxxxx/',
      repoHint: '多个地址使用换行分隔，仅支持Github仓库',
      close: '关闭',
      save: '保存',
      saveSuccess: '插件仓库保存成功',
      saveFailed: '插件仓库保存失败：{message}！',
    },
    userAuth: {
      title: '用户认证',
      codeLabel: '认证码',
      codePlaceholder: '请输入认证码',
      authBtn: '开始认证',
      closeBtn: '关闭',
      selectSite: '选择认证站点',
      selectSiteRequired: '请选择认证站点！',
      siteConfigNotExist: '站点配置不存在！',
      fieldRequired: '请输入{name}！',
      authSuccess: '用户认证成功，请重新登录！',
      authFailed: '认证失败：{message}',
    },
    transferQueue: {
      title: '整理队列',
      name: '名称',
      type: '类型',
      state: '状态',
      progress: '进度',
      startTime: '开始时间',
      speedTitle: '速度',
      pathTitle: '路径',
      sizeTitle: '大小',
      waitingState: '等待中',
      runningState: '正在整理',
      finishedState: '完成',
      failedState: '失败',
      cancelledState: '已取消',
      noTasks: '没有正在整理的任务',
      processing: '请稍候 ...',
      stopAll: '全部停止',
      startAll: '全部开始',
      refresh: '刷新',
      close: '关闭',
    },
    reorganize: {
      title: '整理',
      sourceTitle: '源文件',
      targetTitle: '目标文件',
      processingTitle: '处理中',
      confirmTitle: '确认',
      selectFile: '选择文件',
      selectTarget: '选择目标',
      selectMediaType: '选择媒体类型',
      movie: '电影',
      tv: '电视剧',
      selectTmdbId: '选择TMDB ID',
      selectMediaInfo: '选择媒体信息',
      selectTargetPath: '选择目标路径',
      selectTargetDir: '选择目标目录',
      selectFileName: '选择文件名',
      confirmMoving: '请确认移动！',
      sourceLabel: '源文件：',
      targetLabel: '目标目录：',
      filenameLabel: '文件名：',
      close: '关闭',
      next: '下一步',
      previous: '上一步',
      confirm: '确认',
      manualTitle: '手动整理',
      multipleItemsTitle: '共 {count} 项',
      singleItemTitle: '{path}',
      targetStorage: '目的存储',
      targetStorageHint: '整理目的存储',
      transferType: '整理方式',
      transferTypeHint: '文件操作整理方式',
      targetPath: '目的路径',
      targetPathHint: '整理目的路径，留空将自动匹配',
      targetPathPlaceholder: '留空自动',
      mediaType: '类型',
      mediaTypeHint: '文件的媒体类型',
      tmdbId: 'TheMovieDb编号',
      doubanId: '豆瓣编号',
      mediaIdHint: '按名称查询媒体编号，留空自动识别',
      mediaIdPlaceholder: '留空自动识别',
      episodeGroup: '剧集组编号',
      episodeGroupHint: '指定剧集组',
      episodeGroupPlaceholder: '手动查询剧集组',
      season: '季',
      seasonHint: '第几季',
      episodeDetail: '集',
      episodeDetailHint: '集数或范围，如1或1,2',
      episodeDetailPlaceholder: '起始集,终止集',
      episodeFormat: '集数定位',
      episodeFormatHint: '使用{ep}定位文件名中的集数部分以辅助识别',
      episodeFormatPlaceholder: '使用{ep}定位集数',
      episodeOffset: '集数偏移',
      episodeOffsetHint: '集数偏移运算，如-10或EP*2',
      episodeOffsetPlaceholder: '如-10',
      episodePart: '指定Part',
      episodePartHint: '指定Part，如part1',
      episodePartPlaceholder: '如part1',
      minFileSize: '最小文件大小（MB）',
      minFileSizeHint: '只整理大于最小文件大小的文件',
      typeFolderOption: '按类型分类',
      typeFolderHint: '整理时目的路径下按媒体类型添加子目录',
      categoryFolderOption: '按类别分类',
      categoryFolderHint: '整理时在目的路径下按媒体类别添加子目录',
      scrapeOption: '刮削元数据',
      scrapeHint: '整理完成后自动刮削元数据',
      fromHistoryOption: '复用历史识别信息',
      fromHistoryHint: '使用历史整理记录中已识别的媒体信息',
      addToQueue: '加入整理队列',
      reorganizeNow: '立即整理',
      auto: '自动',
      processing: '正在处理 ...',
      successMessage: '文件 {name} 已加入整理队列！',
    },
    subscribeEdit: {
      titleDefault: '默认订阅规则',
      titleEdit: '编辑订阅',
      seasonFormat: '第 {number} 季',
      tabs: {
        basic: '基础',
        advance: '进阶',
      },
      searchKeyword: '搜索关键词',
      searchKeywordHint: '指定搜索站点时使用的关键词',
      totalEpisode: '总集数',
      totalEpisodeHint: '剧集总集数',
      startEpisode: '开始集数',
      startEpisodeHint: '开始订阅集数',
      quality: '质量',
      qualityHint: '订阅资源质量',
      resolution: '分辨率',
      resolutionHint: '订阅资源分辨率',
      effect: '特效',
      effectHint: '订阅资源特效',
      subscribeSites: '订阅站点',
      subscribeSitesHint: '订阅的站点范围，不选使用系统设置',
      downloader: '下载器',
      downloaderHint: '指定该订阅使用的下载器',
      savePath: '保存路径',
      savePathHint: '指定该订阅的下载保存路径，留空自动使用设定的下载目录',
      bestVersion: '洗版',
      bestVersionHint: '根据洗版优先级进行洗版订阅',
      searchImdbid: '使用 ImdbID 搜索',
      searchImdbidHint: '开使用 ImdbID 精确搜索资源',
      showEditDialog: '订阅时编辑更多规则',
      showEditDialogHint: '添加订阅时显示此编辑订阅对话框',
      include: '包含（关键字、正则式）',
      includeHint: '包含规则，支持正则表达式',
      exclude: '排除（关键字、正则式）',
      excludeHint: '排除规则，支持正则表达式',
      filterGroups: '优先级规则组',
      filterGroupsHint: '按选定的过滤规则组对订阅进行过滤',
      episodeGroup: '指定剧集组',
      episodeGroupHint: '按特定剧集组识别和刮削',
      season: '指定季',
      seasonHint: '指定任意季订阅',
      mediaCategory: '自定义类别',
      mediaCategoryHint: '指定类别名称，留空自动识别',
      customWords: '自定义识别词',
      customWordsHint: '只对该订阅使用的识别词',
      customWordsPlaceholder:
        '屏蔽词\n被替换词 => 替换词\n前定位词 <> 后定位词 >> 集偏移量（EP）\n被替换词 => 替换词 && 前定位词 <> 后定位词 >> 集偏移量（EP）\n其中替换词支持格式：&#123; tmdbid/doubanid=xxx;type=movie/tv;s=xxx;e=xxx &#125; 直接指定TMDBID/豆瓣ID识别，其中s、e为季数和集数（可选）',
      cancelSubscribe: '取消订阅',
      save: '保存',
      cancelSubscribeConfirm: '是否确认取消订阅？',
    },
    subscribeFiles: {
      title: '已下载文件',
      noFilesMessage: '暂无文件',
      close: '关闭',
      downloadTab: '下载文件',
      libraryTab: '媒体库文件',
      episodeColumn: '集',
      torrentColumn: '种子',
      fileColumn: '文件',
      itemsPerPage: '每页条数',
      pageText: '{0}-{1} 共 {2} 条',
      loadingText: '加载中...',
      noData: '没有数据',
      season: '第 {number} 季',
    },
    subscribeHistory: {
      title: '{type}订阅历史',
      resubscribe: '重新订阅',
      resubscribeMovie: '正在重新订阅 {name}...',
      resubscribeTv: '正在重新订阅 {name} 第 {season} 季...',
      season: '第 {season} 季',
      noData: '没有已完成的订阅',
    },
    siteUserData: {
      title: '站点用户数据',
      updateTime: '更新时间',
      username: '用户名',
      uploadTitle: '上传量',
      uploadTotal: '总上传量',
      downloadTitle: '下载量',
      downloadTotal: '总下载量',
      seedingTitle: '做种数',
      seedingCount: '总做种数',
      seedingSize: '总做种体积',
      userLevel: '用户等级',
      msgCount: '未读消息',
      inviteCount: '邀请数',
      bonus: '积分',
      ratio: '分享率',
      joinTime: '加入时间',
      trafficHistory: '历史流量',
      seedingDistribution: '做种分布',
      volumeTitle: '体积',
      countTitle: '数量：',
      noData: '无',
      refreshing: '正在刷新站点数据...',
      close: '关闭',
    },
    siteResource: {
      browseTitle: '浏览 - {name}',
      searchKeyword: '搜索关键字',
      resourceCategory: '资源分类',
      search: '搜索',
      itemsPerPage: '每页条数',
      noData: '没有数据',
      loading: '加载中...',
      titleColumn: '标题',
      timeColumn: '时间',
      sizeColumn: '大小',
      seedersColumn: '做种',
      peersColumn: '下载',
      viewDetails: '查看详情',
      downloadTorrent: '下载种子文件',
      pageText: '{0}-{1} 共 {2} 条',
    },
    forkSubscribe: {
      title: '复制订阅',
      selectSubscriber: '选择复制目标',
      overwriteExisting: '覆盖现有订阅',
      overwriteExistingHint: '目标用户已存在该订阅时，是否覆盖',
      confirm: '确认',
      cancel: '取消',
    },
  },
  file: {
    newFolder: '新建文件夹',
    autoRecognizeName: '自动识别名称',
    createFolder: '创建文件夹',
    fileName: '文件名',
    fileSize: '文件大小',
    fileType: '文件类型',
    lastModified: '修改时间',
    actions: '操作',
    rename: '重命名',
    delete: '删除',
    confirmFileDelete: '确认删除',
    upload: '上传',
    download: '下载',
    preview: '预览',
    selectAll: '全选',
    deselectAll: '取消全选',
    moveUp: '返回上一级',
    sortByName: '按名称排序',
    sortByTime: '按时间排序',
    currentName: '当前名称',
    newName: '新名称',
    includeSubfolders: '自动重命名目录内所有媒体文件',
    emptyFolder: '空文件夹',
    noFilesInFolder: '该文件夹内没有文件',
    autoRecognize: '自动识别名称',
    directoryTree: '目录树',
    rootDirectory: '根目录',
    noDirectories: '没有可用的目录',
    directory: '目录',
    file: '文件',
    size: '大小',
    modifyTime: '修改时间',
    noFiles: '没有目录或文件',
    emptyDirectory: '空目录',
    confirmDelete: '是否确认删除{type} {name}？',
    confirmBatchDelete: '是否确认删除选中的 {count} 个项目？',
    deleting: '正在删除 {name}...',
    recognize: '识别',
    recognizing: '正在识别 {path}...',
    recognizeFailed: '{path} 识别失败！',
    scrape: '刮削',
    scraping: '正在刮削 {path}...',
    scrapeCompleted: '{path} 削刮完成！',
    confirmScrape: '是否确认刮削 {path}？',
    confirmBatchScrape: '是否确认刮削选中的 {count} 项？',
    renaming: '正在重命名 {name}...',
    renamingAll: '正在重命名 {path} 及目录内所有文件...',
    close: '关闭',
    loadingDirectoryStructure: '加载目录结构...',
    reorganize: '整理',
  },
  person: {
    alias: '别名：',
    credits: '参演作品',
    biography: '个人简介',
    birthday: '出生日期',
    placeOfBirth: '出生地',
  },
  error: {
    title: '出错啦！',
    networkError: '无法获取到媒体信息，请检查网络连接。',
    serverError: '服务器错误，请稍后重试。',
    notFound: '找不到请求的资源。',
  },
  plugin: {
    sort: {
      popular: '热门',
      name: '插件名称',
      author: '作者',
      repository: '插件仓库',
      latest: '最新发布',
    },
    installingPlugin: '正在安装插件...',
    installing: '正在安装 {name} v{version} ...',
    installSuccess: '插件 {name} 安装成功！',
    installFailed: '插件 {name} 安装失败：{message}',
    filterPlugins: '过滤插件',
    name: '名称',
    hasNewVersion: '有新版本',
    running: '运行中',
    author: '作者',
    label: '标签',
    repository: '仓库',
    sortTitle: '排序',
    filter: '过滤：{name}',
    noMatchingContent: '没有找到匹配的内容',
    pleaseInstallFromMarket: '请从插件市场安装插件',
    allPluginsInstalled: '所有插件已安装',
    searchPlugins: '搜索插件',
    searchPlaceholder: '按插件名称或描述搜索',
    uninstalling: '正在卸载 {name} ...',
    uninstallSuccess: '插件 {name} 卸载成功！',
    uninstallFailed: '插件 {name} 卸载失败：{message}',
    updating: '正在更新 {name} ...',
    updateSuccess: '插件 {name} 更新成功！',
    updateFailed: '插件 {name} 更新失败：{message}',
    noPlugins: '没有安装插件',
    installed: '已安装',
    notInstalled: '未安装',
    hasUpdate: '有更新',
    configuring: '配置',
    enable: '启用',
    disable: '禁用',
    settings: '设置',
    projectHome: '项目主页',
    updateHistory: '更新说明',
    installToLocal: '安装到本地',
    totalDownloads: '共 {count} 次下载',
    viewData: '查看数据',
    update: '更新',
    reset: '重置',
    uninstall: '卸载',
    viewLogs: '查看日志',
    authorHome: '作者主页',
    confirmUninstall: '是否确认卸载插件 {name}？',
    confirmReset: '此操作将恢复插件 {name} 的默认设置，并清除所有相关数据，确定要继续吗？',
    resetSuccess: '插件 {name} 数据已重置',
    resetFailed: '插件 {name} 重置失败：{message}',
    updateHistoryTitle: '{name} 更新说明',
    updateToLatest: '更新到最新版本',
    updatingTo: '更新 {name} 到 {version} 版本...',
    folderNameEmpty: '文件夹名称不能为空',
    folderExists: '文件夹已存在',
    folderCreateSuccess: '文件夹创建成功',
    folderRenameSuccess: '文件夹重命名成功',
    folderRenameFailed: '重命名文件夹失败',
    folderDeleteSuccess: '文件夹删除成功',
    folderDeleteFailed: '删除文件夹失败',
    removeFromFolderSuccess: '插件已移出文件夹',
    operationFailed: '操作失败',
    saveFolderConfigFailed: '保存文件夹配置失败',
    newFolder: '新建文件夹',
    folderName: '文件夹名称',
    cancel: '取消',
    create: '创建',
    clone: '分身',
    cloneTitle: '创建插件分身',
    cloneSubtitle: '为 {name} 创建独立的分身实例',
    cloneFeature: '插件分身功能',
    cloneDescription: '创建插件的独立副本，拥有独立的配置和数据，适用于多账号、测试环境等场景',
    suffix: '分身后缀',
    suffixPlaceholder: '例如：Test、Backup、Site1',
    suffixHint: '用于区分分身的唯一标识，只能包含英文字母和数字',
    suffixRequired: '分身后缀不能为空',
    suffixFormatError: '只能包含英文字母和数字',
    suffixLengthError: '长度不能超过20个字符',
    cloneName: '分身名称',
    cloneNamePlaceholder: '例如：自动备份 测试版',
    cloneNameHint: '分身插件的显示名称（可选）',
    cloneDefaultName: '{name} 分身',
    cloneDescriptionLabel: '分身描述',
    cloneDescriptionPlaceholder: '描述这个分身的用途和特点...',
    cloneDescriptionHint: '详细描述分身插件的用途（可选）',
    cloneDefaultDescription: '{description} (分身版本)',
    cloneVersion: '版本号',
    cloneVersionPlaceholder: '例如：1.0、2.1.0',
    cloneVersionHint: '自定义分身插件的版本号（可选）',
    cloneIcon: '图标URL',
    cloneIconPlaceholder: 'https://example.com/icon.png',
    cloneIconHint: '自定义分身插件的图标（可选）',
    cloneNotice: '分身插件创建后默认为禁用状态，需要手动配置启用。分身后缀一旦确定无法修改。',
    createClone: '创建分身',
    cloning: '正在创建 {name} 的分身...',
    cloneSuccess: '插件分身 {name} 创建成功！',
    cloneFailed: '插件分身创建失败：{message}',
    cloneFailedGeneral: '插件分身创建失败',
    logTitle: '插件日志',
    quickAccess: '快速访问',
    tapToOpen: '点击返回主界面',
    noPluginsWithPage: '暂无可用插件',
    recentlyUsed: '最近使用',
    allPlugins: '所有插件',
    noRecentPlugins: '无',
  },
  profile: {
    personalInfo: '个人信息',
    uploadNewAvatar: '上传新头像',
    avatarFormatError: '上传的文件不符合要求，请重新选择头像',
    avatarSizeError: '文件大小不得大于800KB',
    avatarUploadSuccess: '新头像上传成功，待保存后生效!',
    resetAvatarSuccess: '已重置为默认头像，待保存后生效！',
    restoreAvatarSuccess: '已还原当前使用头像！',
    savingInProgress: '正在保存中，请稍后...',
    usernameRequired: '用户名不能为空',
    passwordMismatch: '两次输入的密码不一致',
    usernameChangeSuccess: '【{oldName}】更名【{newName}】，用户信息保存成功！',
    saveSuccess: '用户信息保存成功！',
    saveFailedWithNameChange: '【{oldName}】更名【{newName}】，信息保存失败：{message}！',
    saveFailed: '用户信息保存失败：{message}！',
    nickname: '昵称',
    nicknamePlaceholder: '显示昵称，优先于用户名显示',
    accountBinding: '账号绑定',
    wechatUser: '微信用户',
    telegramUser: 'Telegram用户',
    slackUser: 'Slack用户',
    vocechatUser: 'VoceChat用户',
    synologychatUser: 'SynologyChat用户',
    doubanUser: '豆瓣用户',
    twoFactorAuthentication: '登录双重验证',
    enableTwoFactor: '开启双重验证',
    disableTwoFactor: '关闭双重验证',
    otpGenerateFailed: '获取otp uri失败：{message}！',
    otpDisableSuccess: '关闭登录双重验证成功！',
    otpDisableFailed: '关闭otp失败：{message}！',
    otpCodeRequired: '请填写6位验证码',
    otpEnableSuccess: '开启登录双重验证成功！',
    otpEnableFailed: '开启otp失败：{message}！',
    authenticatorApp: '身份验证器',
    authenticatorAppDescription:
      '使用像Google Authenticator、Microsoft Authenticator、Authy或1Password这样的身份验证器应用程序，扫描二维码。它将为您生成一个6位数的代码，供您在下方输入。',
    secretKeyTip: '如果您在使用二维码时遇到困难，请在您的应用程序中选择手动输入以上代码。',
    enterVerificationCode: '输入验证码以确认开启双重验证',
    avatarFormatTip: '允许 JPG、PNG、GIF、WEBP 格式， 最大尺寸 800KB。',
  },
  transferHistory: {
    title: '转移历史',
    searchPlaceholder: '搜索转移记录',
    titleColumn: '标题',
    pathColumn: '路径',
    modeColumn: '转移方式',
    sizeColumn: '大小',
    dateColumn: '时间',
    statusColumn: '状态',
    actionsColumn: '操作',
    seasonEpisode: '季集/类别',
    transferQueue: '转移队列',
    groupMode: '分组模式',
    listMode: '列表模式',
    deleteConfirm: '确认删除 {title} {seasons}{episodes} ?',
    deleteConfirmBatch: '确认删除 {count} 条记录 ?',
    deleteRecordOnly: '仅删除转移记录',
    deleteSourceOnly: '删除转移记录和源文件',
    deleteDestOnly: '删除转移记录和媒体库文件',
    deleteAll: '删除转移记录、源文件和媒体库文件',
    transferMode: {
      copy: '复制',
      move: '移动',
      link: '硬链接',
      softlink: '软链接',
      rclone_copy: 'Rclone复制',
      rclone_move: 'Rclone移动',
    },
    status: {
      success: '成功',
      failed: '失败',
      unknown: '未知',
    },
    noData: '没有数据',
    loading: '加载中...',
    pageSize: '每页条数',
    pageInfo: '{begin} - {end} / {total}',
    actions: {
      redo: '重新整理',
      delete: '删除',
    },
    progress: {
      processing: '处理中',
      pleaseWait: '请稍候...',
    },
    table: {
      emptyTitle: '操作',
    },
  },
  customRule: {
    error: {
      emptyIdName: '规则ID和规则名称不能为空',
      idOccupied: '当前规则ID已被内置规则占用',
      nameOccupied: '当前规则名称已被内置规则占用',
      idExists: '规则ID【{id}】已存在',
      nameExists: '规则名称【{name}】已存在',
    },
    title: '{id} - 配置',
    field: {
      ruleId: '规则ID',
      ruleName: '规则名称',
      include: '包含',
      exclude: '排除',
      sizeRange: '资源体积（MB）',
      seeders: '做种人数',
      publishTime: '发布时间（分钟）',
    },
    placeholder: {
      ruleId: '必填；不可与其他规则ID重名',
      ruleName: '必填；不可与其他规则名称重名',
      include: '关键字/正则表达式',
      exclude: '关键字/正则表达式',
      sizeRange: '0/1-10',
      seeders: '0/1-10',
      publishTime: '0/1-10',
    },
    hint: {
      ruleId: '字符与数字组合，不能含空格',
      ruleName: '使用别名便于区分规则',
      include: '必须包含的关键字或正则表达式，多个值使用｜分隔',
      exclude: '不能包含的关键字或正则表达式，多个值使用｜分隔',
      sizeRange: '最小资源文件体积或体积范围（剧集计算单集平均大小）',
      seeders: '最小做种人数或做种人数范围',
      publishTime: '距离资源发布的最小时间间隔或时间区间',
    },
    action: {
      confirm: '确定',
    },
  },
  downloader: {
    title: '下载器',
    name: '名称',
    type: '类型',
    enabled: '启用',
    customTypeHint: '自定义下载器类型，用于插件等场景',
    default: '默认',
    host: '地址',
    username: '用户名',
    password: '密码',
    category: '自动分类管理',
    sequentail: '顺序下载',
    force_resume: '强制继续',
    first_last_piece: '优先首尾文件',
    saveSuccess: '下载器设置保存成功',
    saveFailed: '下载器设置保存失败',
    nameRequired: '不能为空，且不能重名',
    nameDuplicate: '名称已存在',
    defaultChanged: '存在默认下载器，已替换',
  },
  filterRule: {
    title: '过滤规则',
    groupName: '规则组名称',
    priority: '优先级',
    rules: '规则',
    add: '添加规则',
    import: '导入规则',
    share: '分享规则',
    save: '保存规则',
    nameRequired: '规则组名称不能为空',
    nameDuplicate: '规则组名称已存在',
    importSuccess: '规则导入成功',
    importFailed: '规则导入失败',
    shareSuccess: '规则已复制到剪贴板',
    shareFailed: '规则复制失败',
    mediaType: '媒体类型',
    category: '媒体类别',
    mediaTypeItems: {
      movie: '电影',
      tv: '电视剧',
      anime: '动漫',
      collection: '合集',
      unknown: '未知',
    },
  },
  mediaserver: {
    type: '类型',
    customTypeHint: '自定义媒体服务器类型，用于插件等场景',
    enableMediaServer: '启用媒体服务器',
    nameRequired: '必填，不可重名',
    serverAlias: '媒体服务器的别名',
    host: '地址',
    hostPlaceholder: 'http(s)://ip:port',
    hostHint: '服务端地址，格式：http(s)://ip:port',
    playHost: '外网播放地址',
    playHostPlaceholder: 'http(s)://domain:port',
    playHostHint: '跳转播放页面使用的地址，格式：http(s)://domain:port',
    apiKey: 'API密钥',
    embyApiKeyHint: 'Emby设置->高级->API密钥中生成的密钥',
    jellyfinApiKeyHint: 'Jellyfin设置->高级->API密钥中生成的密钥',
    plexToken: 'X-Plex-Token',
    plexTokenHint: '浏览器F12->网络，从Plex请求URL中获取的X-Plex-Token',
    username: '用户名',
    password: '密码',
    syncLibraries: '同步媒体库',
    syncLibrariesHint: '只有选中的媒体库才会被同步',
    nameExists: '【{name}】已存在，请替换为其他名称',
  },
  bangumi: {
    category: '类别',
    sort: '排序',
    year: '年份',
    cat: {
      other: '其他',
      tv: 'TV',
      ova: 'OVA',
      movie: 'Movie',
      web: 'WEB',
    },
    sortType: {
      rank: '排名',
      date: '日期',
    },
  },
  tmdb: {
    type: '类型',
    sort: '排序',
    genre: '风格',
    language: '语言',
    rating: '评分',
    sortType: {
      popularityDesc: '热度降序',
      popularityAsc: '热度升序',
      releaseDateDesc: '上映日期降序',
      releaseDateAsc: '上映日期升序',
      firstAirDateDesc: '首播日期降序',
      firstAirDateAsc: '首播日期升序',
      voteAverageDesc: '评分降序',
      voteAverageAsc: '评分升序',
    },
    genreType: {
      action: '动作',
      adventure: '冒险',
      animation: '动画',
      comedy: '喜剧',
      crime: '犯罪',
      documentary: '纪录片',
      drama: '剧情',
      family: '家庭',
      fantasy: '奇幻',
      history: '历史',
      horror: '恐怖',
      music: '音乐',
      mystery: '悬疑',
      romance: '爱情',
      scienceFiction: '科幻',
      tvMovie: '电视电影',
      thriller: '惊悚',
      war: '战争',
      western: '西部',
      actionAdventure: '动作冒险',
      kids: '儿童',
      news: '新闻',
      reality: '真人秀',
      sciFiFantasy: '科幻奇幻',
      soap: '肥皂剧',
      talk: '戏剧',
      warPolitics: '战争政治',
    },
    languageType: {
      zh: '中文',
      en: '英语',
      ja: '日语',
      ko: '韩语',
      fr: '法语',
      de: '德语',
      es: '西班牙语',
      it: '意大利语',
      ru: '俄语',
      pt: '葡萄牙语',
      ar: '阿拉伯语',
      hi: '印地语',
      th: '泰语',
    },
  },
  douban: {
    type: '类型',
    sort: '排序',
    genre: '风格',
    zone: '地区',
    year: '年代',
    sortType: {
      comprehensive: '综合排序',
      releaseDate: '首播时间',
      recentHot: '近期热度',
      highScore: '高分优先',
    },
    genreType: {
      comedy: '喜剧',
      romance: '爱情',
      action: '动作',
      scienceFiction: '科幻',
      animation: '动画',
      mystery: '悬疑',
      crime: '犯罪',
      thriller: '惊悚',
      adventure: '冒险',
      music: '音乐',
      history: '历史',
      fantasy: '奇幻',
      horror: '恐怖',
      war: '战争',
      biography: '传记',
      musical: '歌舞',
      martialArts: '武侠',
      erotic: '情色',
      disaster: '灾难',
      western: '西部',
      documentary: '纪录片',
      shortFilm: '短片',
    },
    zoneType: {
      chinese: '华语',
      europeanAmerican: '欧美',
      korean: '韩国',
      japanese: '日本',
      mainlandChina: '中国大陆',
      usa: '美国',
      hongKong: '中国香港',
      taiwan: '中国台湾',
      uk: '英国',
      france: '法国',
      germany: '德国',
      italy: '意大利',
      spain: '西班牙',
      india: '印度',
      thailand: '泰国',
      russia: '俄罗斯',
      canada: '加拿大',
      australia: '澳大利亚',
      ireland: '爱尔兰',
      sweden: '瑞典',
      brazil: '巴西',
      denmark: '丹麦',
    },
    yearType: {
      '2020s': '2020年代',
      '2010s': '2010年代',
      '2000s': '2000年代',
      '1990s': '90年代',
      '1980s': '80年代',
      '1970s': '70年代',
      '1960s': '60年代',
    },
  },
  directory: {
    alias: '目录别名',
    mediaType: '媒体类型',
    mediaCategory: '媒体类别',
    resourceStorage: '资源存储',
    resourceDirectory: '资源目录',
    sortByType: '按类型分类',
    sortByCategory: '按类别分类',
    autoTransfer: '自动整理',
    monitorMode: '监控模式',
    libraryStorage: '媒体库存储',
    libraryDirectory: '媒体库目录',
    transferType: '整理方式',
    overwriteMode: '覆盖模式',
    smartRename: '智能重命名',
    scrapingMetadata: '刮削元数据',
    sendNotification: '发送通知',
    noTransfer: '不整理',
    downloaderMonitor: '下载器监控',
    directoryMonitor: '目录监控',
    manualTransfer: '手动整理',
    performanceMode: '性能模式',
    compatibilityMode: '兼容模式',
    pleaseSelectStorage: '请选择存储',
    pleaseSelectLibraryStorage: '请选择媒体库存储',
    pleaseSelectDownloadStorage: '请选择下载存储',
    noSupportedTransferType: '没有支持的整理方式',
    never: '从不',
    always: '总是',
    byFileSize: '按文件大小',
    keepLatestOnly: '仅保留最新',
  },
  validators: {
    required: '此项为必填项',
    number: '请输入数字',
  },
  folder: {
    settingAppearance: '设置外观',
    rename: '重命名',
    deleteFolder: '删除文件夹',
    folderNameCannotBeEmpty: '文件夹名称不能为空',
    confirmDeleteFolder: '确定要删除文件夹 "{folderName}" 吗？文件夹中的插件将移回主列表。',
    folderSettingsSaved: '文件夹设置已保存',
    renameFolder: '重命名文件夹',
    folderName: '文件夹名称',
    folderAppearanceSettings: '文件夹外观设置',
    showFolderIcon: '显示文件夹图标',
    icon: '图标',
    iconColor: '图标颜色',
    backgroundGradient: '背景渐变',
    customBackgroundImageURL: '自定义背景图片URL（可选）',
    customBackgroundImageHint: '支持网络图片URL，留空则使用渐变背景',
    pluginCount: '{count} 个插件',
  },
}
