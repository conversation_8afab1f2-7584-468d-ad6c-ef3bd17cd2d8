export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    close: 'Close',
    version: 'Version',
    author: 'Author',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    loading: 'Loading',
    success: 'Success',
    error: 'Error',
    openInNewWindow: 'Open in new window',
    inputMessage: 'Enter message or command',
    send: 'Send',
    noData: 'No data',
    noContent: 'No relevant content found',
    all: 'All',
    active: 'Active',
    inactive: 'Inactive',
    filter: 'Filter',
    noMatchingData: 'No matching data',
    tryChangingFilters: 'Try changing filters',
    default: 'Default',
    name: 'Name',
    create: 'Create',
    saving: 'Saving',
    reset: 'Reset',
    theme: 'Theme',
    language: 'Language',
    pleaseWait: 'Please wait...',
    viewDetails: 'View Details',
    user: 'User',
    config: 'Configuration',
    pause: 'Pause',
    enable: 'Enable',
    confirmAction: 'Confirm {action}',
    details: 'Details',
    files: 'Files',
    share: 'Share',
    subscribe: 'Subscribe',
    unsubscribe: 'Unsubscribe',
    media: 'Media',
    unknown: 'Unknown',
    notice: 'Notice',
    itemsPerPage: 'Items per page',
    pageText: '{0}-{1} of {2}',
    noDataText: 'No data',
    loadingText: 'Loading...',
    networkRequired: 'This feature requires network connection',
    networkDisconnected: 'Network connection lost',
    featuresLimited: 'Some features may be limited',
    serverConnectionFailed: 'Server connection failed',
    troubleshooting: 'Troubleshooting',
    checking: 'Checking',
    retry: 'Retry',
    networkOnline: 'Network Online',
    networkOffline: 'Network Offline',
    serviceAvailable: 'Service Available',
    serviceUnavailable: 'Service Unavailable',
    status: 'Status',
    preset: 'Preset',
  },
  mediaType: {
    movie: 'Movie',
    tv: 'TV Show',
    anime: 'Anime',
    collection: 'Collection',
    unknown: 'Unknown',
  },
  notificationSwitch: {
    resourceDownload: 'Resource Download',
    organize: 'Organize',
    subscribe: 'Subscribe',
    site: 'Site',
    mediaServer: 'Media Server',
    manual: 'Manual',
    plugin: 'Plugin',
    other: 'Other',
  },
  actionStep: {
    addDownload: 'Add Download',
    addSubscribe: 'Add Subscribe',
    fetchDownloads: 'Fetch Downloads',
    fetchMedias: 'Fetch Media',
    fetchRss: 'Fetch RSS',
    fetchTorrents: 'Fetch Torrents',
    filterMedias: 'Filter Media',
    filterTorrents: 'Filter Torrents',
    scanFile: 'Scan Directory',
    scrapeFile: 'Scrape File',
    sendEvent: 'Send Event',
    sendMessage: 'Send Message',
    transferFile: 'Transfer File',
    invokePlugin: 'Invoke Plugin',
    note: 'Note',
  },
  qualityOptions: {
    all: 'All',
    blurayOriginal: 'Blu-ray Original',
    remux: 'Remux',
    bluray: 'BluRay',
    uhd: 'UHD',
    webdl: 'WEB-DL',
    hdtv: 'HDTV',
    h265: 'H265',
    h264: 'H264',
  },
  resolutionOptions: {
    all: 'All',
    '4k': '4K',
    '1080p': '1080p',
    '720p': '720p',
  },
  effectOptions: {
    all: 'All',
    dolbyVision: 'Dolby Vision',
    dolbyAtmos: 'Dolby Atmos',
    hdr: 'HDR',
    sdr: 'SDR',
  },
  theme: {
    light: 'Light',
    dark: 'Dark',
    auto: 'Follow System',
    transparent: 'Transparent',
    purple: 'Purple',
    custom: 'Custom Style',
    transparency: 'Transparency',
    transparencyAdjust: 'Transparency Adjustment',
    transparencyOpacity: 'Opacity',
    transparencyBlur: 'Blur',
    transparencyReset: 'Reset',
    transparencyLow: 'Low Transparency',
    transparencyMedium: 'Medium Transparency',
    transparencyHigh: 'High Transparency',
    customCssSaveSuccess: 'Custom CSS saved successfully, please refresh the page to take effect!',
    customCssSaveFailed: 'Failed to save custom CSS to server',
    deviceNotSupport: 'Current device does not support monitoring system theme changes',
  },
  app: {
    moviepilot: 'MoviePilot',
    slogan: 'Intelligent Movie & TV Media Library Management Tool',
    recommend: 'Recommend',
    subscribeMovie: 'Movie Subscription',
    subscribeTv: 'TV Subscription',
    settings: 'Settings',
    selectLanguage: 'Select Language',
    logout: 'Logout',
    restarting: 'Restarting...',
    confirmRestart: 'Confirm restart system?',
    restartTip: 'After restart, you will be logged out and need to log in again.',
    restartTimeout: 'Restart timeout, the system may need more time to recover, please refresh the page manually later',
    restartFailed: 'Restart failed, please check system status',
    offline: 'Application Offline',
    offlineMessage: 'Network connection lost, some features may be limited',
    online: 'Application Online',
    onlineMessage: 'Network connection restored',
  },
  pwa: {
    installApp: 'Install MoviePilot App',
    installDescription: 'Get better offline experience and performance',
    install: 'Install',
    installSuccess: 'App installed successfully!',
    installGuide: 'Installation Guide',
    installInstructions: 'Install MoviePilot on {platform}:',
    installNote:
      'After installation, you can quickly access MoviePilot from your home screen and enjoy offline features.',
    gotIt: 'Got it',
    // Platform specific descriptions
    platforms: {
      ios: 'iOS',
      android: 'Android',
      chrome: 'Chrome',
      edge: 'Edge',
      firefox: 'Firefox',
      safari: 'Safari',
      desktop: 'Desktop',
      mobile: 'Mobile',
      other: 'Other Browser',
    },
    // Installation steps
    installSteps: {
      ios: {
        0: 'Tap the share button at the bottom of the browser',
        1: 'Select "Add to Home Screen"',
        2: 'Tap "Add" to confirm installation',
      },
      android: {
        0: 'Tap the browser menu (three dots)',
        1: 'Select "Add to Home Screen" or "Install App"',
        2: 'Tap "Install" to confirm',
      },
      chrome: {
        0: 'Click the install icon in the address bar',
        1: 'Or click "Install MoviePilot" in the browser menu',
        2: 'Click "Install" to confirm',
      },
      edge: {
        0: 'Click the app icon in the address bar',
        1: 'Select "Install this site as an app"',
        2: 'Click "Install" to confirm',
      },
      firefox: {
        0: 'Click the install icon in the address bar',
        1: 'Select "Install"',
        2: 'Confirm installation to desktop',
      },
      safari: {
        0: 'Click the share button',
        1: 'Select "Add to Home Screen"',
        2: 'Tap "Add" to confirm',
      },
      desktop: {
        0: 'Click the install icon in the address bar',
        1: 'Select "Install App"',
        2: 'Follow the prompts to complete installation',
      },
      mobile: {
        0: 'Tap the browser menu',
        1: 'Select "Add to Home Screen"',
        2: 'Confirm installation',
      },
      other: {
        0: 'Look for "Install" option in your browser',
        1: 'Usually in the address bar or menu',
        2: 'Follow the prompts to complete installation',
      },
    },
  },
  login: {
    wallpapers: 'Wallpapers',
    username: 'Username',
    password: 'Password',
    otpCode: 'Two-Factor Code',
    stayLoggedIn: 'Stay Logged In',
    login: 'Login',
    networkError: 'Login failed, please check your network connection!',
    authFailure: 'Login failed, please check your username, password or two-factor authentication!',
    permissionDenied: 'Login failed, you do not have permission to access!',
    noPermission: 'Login failed, you have no functional permissions, please contact the administrator!',
    serverError: 'Login failed, server error!',
    loginFailed: 'Login Failed',
    checkCredentials: 'Please check your username, password or two-factor authentication code!',
  },
  menu: {
    start: 'Start',
    discovery: 'Discovery',
    subscribe: 'Subscribe',
    organize: 'Organize',
    system: 'System',
  },
  navItems: {
    dashboard: 'Dashboard',
    mediaInfo: 'Media Library',
    recommend: 'Recommend',
    site: 'Sites',
    search: 'Search',
    searchResult: 'Search Results',
    download: 'Download',
    movieSubscribe: 'Movie Subscription',
    tvSubscribe: 'TV Subscription',
    history: 'History',
    transfer: 'Organize',
    rename: 'Rename',
    statistic: 'Statistics',
    setting: 'Settings',
    plugin: 'Plugins',
    user: 'Users',
    about: 'About',
    explore: 'Explore',
    movie: 'Movies',
    tv: 'TV Shows',
    workflow: 'Workflow',
    calendar: 'Calendar',
    downloadManager: 'Download Manager',
    mediaOrganize: 'Media Organize',
    fileManager: 'File Manager',
    pluginManager: 'Plugins',
    siteManager: 'Site Management',
    userManager: 'User Management',
    settings: 'Settings',
  },
  settingTabs: {
    system: {
      title: 'System',
      description: 'Basic settings, downloaders (Qbittorrent, Transmission), media servers (Emby, Jellyfin, Plex)',
    },
    directory: {
      title: 'Storage & Directories',
      description: 'Download directory, media library directory, organization, scraping',
    },
    site: {
      title: 'Sites',
      description: 'Site synchronization, site data refresh, site reset',
    },
    rule: {
      title: 'Rules',
      description: 'Custom rules, priority rule groups, download rules',
    },
    search: {
      title: 'Search & Download',
      description: 'Search data sources (TheMovieDb, Douban, Bangumi), download task tags, search sites',
    },
    subscribe: {
      title: 'Subscription',
      description: 'Subscription sites, subscription mode, subscription rules, version upgrade rules',
    },
    scheduler: {
      title: 'Services',
      description: 'Scheduled jobs',
    },
    cache: {
      title: 'Cache',
      description: 'Torrent cache, media recognition data cache, image file cache management',
    },
    notification: {
      title: 'Notifications',
      description: 'Notification channels (WeChat, Telegram, Slack, SynologyChat, VoceChat, WebPush), message scope',
    },
    words: {
      title: 'Word Lists',
      description:
        'Custom recognition words, custom production/subtitle groups, custom placeholders, file organization block words',
    },
    about: {
      title: 'About',
      description: 'Software version',
    },
  },
  subscribeTabs: {
    movie: {
      mysub: 'My Subscriptions',
      popular: 'Popular Subscriptions',
    },
    tv: {
      mysub: 'My Subscriptions',
      popular: 'Popular Subscriptions',
      share: 'Subscription Shares',
    },
  },
  workflowTabs: {
    list: 'My Workflows',
    share: 'Workflow Share',
  },
  pluginTabs: {
    installed: 'My Plugins',
    market: 'Plugin Market',
  },
  discoverTabs: {
    themoviedb: 'TheMovieDb',
    douban: 'Douban',
    bangumi: 'Bangumi',
  },
  user: {
    admin: 'Admin',
    normal: 'Normal User',
    active: 'Active',
    inactive: 'Inactive',
    noEmail: 'No Email',
    movieSubscriptions: 'Movie Subscriptions',
    tvSubscriptions: 'TV Show Subscriptions',
    cannotDeleteCurrentUser: 'Cannot delete the currently logged in user!',
    confirmDeleteUser: 'Are you sure you want to delete all data of user {username}?',
    deleteSuccess: 'User deleted successfully',
    deleteFailed: 'Failed to delete user!',
    profile: 'Profile',
    systemSettings: 'System Settings',
    siteAuth: 'User Authentication',
    helpDocs: 'Help Documents',
    restart: 'Restart',
    management: 'User Management',
    noUsers: 'No Users',
    clickToAddUser: 'Click Add User card to add users',
    addUser: 'Add User',
    editUser: 'Edit User',
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    role: 'Role',
    email: 'Email',
    enabled: 'Enabled',
    disabled: 'Disabled',
    status: 'Status',
    operations: 'Operations',
  },
  nav: {
    more: 'More',
  },
  notification: {
    center: 'Notification Center',
    markRead: 'Mark as Read',
    empty: 'No Notifications',
    channel: 'Notification Channel',
    name: 'Name',
    nameHint: 'Name of notification channel',
    type: 'Type',
    typeHint: 'Type of notification channel',
    customTypeHint: 'Custom notification type, used for plugin implementation scenarios',
    customTypePlaceholder: 'custom',
    nameRequired: 'Please enter name',
    enabled: 'Enabled',
    config: 'Configuration',
    wechat: {
      name: 'WeChat Work',
      corpId: 'Corp ID',
      corpIdHint: 'Corp ID in WeChat Work backend enterprise information',
      appId: 'App AgentId',
      appIdHint: 'AgentId of self-built app in WeChat Work',
      appSecret: 'App Secret',
      appSecretHint: 'Secret of self-built app in WeChat Work',
      proxy: 'Proxy Address',
      proxyHint:
        'Proxy address for WeChat message forwarding, required for self-built apps created after June 20, 2022',
      token: 'Token',
      tokenHint: 'Token in WeChat Work self-built app -> API message receiving configuration',
      encodingAesKey: 'EncodingAESKey',
      encodingAesKeyHint: 'EncodingAESKey in WeChat Work self-built app -> API message receiving configuration',
      admins: 'Admin Whitelist',
      adminsHint: 'User IDs that can use admin menu and commands, separated by commas',
      adminsPlaceholder: 'User IDs list, separated by commas',
    },
    telegram: {
      name: 'Telegram',
      token: 'Bot Token',
      tokenHint: 'Telegram bot token, format: 123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
      chatId: 'Chat ID',
      chatIdHint: 'Chat ID of user, group or channel that receives notifications',
      users: 'User Whitelist',
      usersHint: 'User IDs that can use Telegram bot, separated by commas. Leave empty to allow all users',
      admins: 'Admin Whitelist',
      adminsHint: 'User IDs that can use admin menu and commands, separated by commas',
      adminsPlaceholder: 'User IDs list, separated by commas',
      usersPlaceholder: 'User IDs list, separated by commas',
      apiUrl: 'Proxy Api Url',
      apiUrlHint: 'Custom proxy Api Url, format: https://api.telegram.org',
      apiUrlPlaceholder: 'https://api.telegram.org',
    },
    slack: {
      name: 'Slack',
      oauthToken: 'Slack Bot User OAuth Token',
      oauthTokenHint: 'Bot User OAuth Token in Slack app OAuth & Permissions page',
      appToken: 'Slack App-Level Token',
      appTokenHint: 'App-Level Token in Slack app OAuth & Permissions page',
      channel: 'Channel Name',
      channelHint: 'Channel to send messages, default is "all"',
    },
    synologychat: {
      name: 'Synology Chat',
      webhook: 'Webhook URL',
      webhookHint: 'Synology Chat bot webhook URL',
      token: 'Token',
      tokenHint: 'Synology Chat bot token',
    },
    vocechat: {
      name: 'VoceChat',
      host: 'Address',
      hostHint: 'VoceChat server address, format: http(s)://ip:port',
      apiKey: 'Bot API Key',
      apiKeyHint: 'VoceChat bot API key',
      channelId: 'Channel ID',
      channelIdHint: 'VoceChat channel ID, without #',
    },
    webpush: {
      name: 'WebPush',
      username: 'Login Username',
      usernameHint: 'Only push messages to the corresponding logged-in user',
    },
  },
  shortcut: {
    title: 'Shortcuts',
    recognition: {
      title: 'Recognition',
      subtitle: 'Name Recognition Test',
    },
    rule: {
      title: 'Rules',
      subtitle: 'Rule Testing',
    },
    log: {
      title: 'Logs',
      subtitle: 'Real-time Logs',
    },
    network: {
      title: 'Network',
      subtitle: 'Network Speed and Connectivity Test',
    },
    system: {
      title: 'System',
      subtitle: 'Health Check',
    },
    message: {
      title: 'Messages',
      subtitle: 'Message Center',
    },
  },
  workflow: {
    components: 'Action Components',
    clickToAdd: 'Click to Add',
    dragToCanvas: 'Drag to Canvas',
    tapComponentHint: 'Tap component to add to canvas',
    dragComponentHint: 'Drag component to canvas',
    task: {
      edit: 'Edit Task',
      editFlow: 'Edit Flow',
      share: 'Share',
      continue: 'Continue',
      restart: 'Restart',
      run: 'Run Now',
      reset: 'Reset Task',
      delete: 'Delete Task',
      confirmDelete: 'Are you sure to delete task {name} ?',
      confirmReset: 'Are you sure to reset task {name} ?',
      deleteSuccess: 'Task deleted successfully!',
      deleteFailed: 'Failed to delete task: {message}',
      enableSuccess: 'Task enabled successfully!',
      enableFailed: 'Failed to enable task: {message}',
      pauseSuccess: 'Task paused successfully!',
      pauseFailed: 'Failed to pause task: {message}',
      runSuccess: 'Task execution completed!',
      runFailed: 'Task execution failed: {message}',
      resetSuccess: 'Task reset successfully!',
      resetFailed: 'Failed to reset task: {message}',
      status: {
        success: 'Success',
        running: 'Running',
        failed: 'Failed',
        paused: 'Paused',
        waiting: 'Waiting',
      },
      info: {
        trigger: 'Trigger',
        timer: 'Timer',
        status: 'Status',
        actionCount: 'Action Count',
        runCount: 'Run Count',
        progress: 'Progress',
        error: 'Error Message',
        manualTrigger: 'Manual',
      },
    },
    scanFile: {
      title: 'Scan Directory',
      subtitle: 'Scan directory files to queue',
      storage: 'Storage',
      directory: 'Directory',
    },
    addDownload: {
      title: 'Add Download',
      subtitle: 'Add resource to downloader',
      downloader: 'Downloader',
      category: 'Category',
      savePath: 'Save Path',
      sequential: 'Sequential',
      forceResume: 'Force Resume',
      firstLastPiece: 'First Last Piece',
      onlyLack: 'Only Download Lack Resource',
      categoryPlaceholder: 'Use comma to separate multiple',
      savePathPlaceholder: 'Leave empty for auto',
    },
    addSubscribe: {
      title: 'Add Subscribe',
      subtitle: 'Add resource to subscription',
      type: 'Type',
      name: 'Name',
      season: 'Season',
      episode: 'Episode',
    },
    fetchMedias: {
      title: 'Fetch Media Data',
      subtitle: 'Fetch media data list from rankings',
      source: 'Source',
      searchType: 'Search Type',
      type: 'Type',
      name: 'Name',
      year: 'Year',
      ranking: 'Ranking',
      api: 'Plugin API',
      apiPath: 'API Path',
      selectRanking: 'Select Ranking',
      tmdbTrending: 'TMDB Trending',
      doubanShowing: 'Now Showing',
      bangumiCalendar: 'Bangumi Daily',
      tmdbMovies: 'TMDB Popular Movies',
      tmdbTvs: 'TMDB Popular TV Shows',
      doubanMovieHot: 'Douban Hot Movies',
      doubanTvHot: 'Douban Hot TV Shows',
      doubanTvAnimation: 'Douban Hot Anime',
      doubanMovies: 'Douban Latest Movies',
      doubanTvs: 'Douban Latest TV Shows',
      doubanMovieTop250: 'Douban Movie TOP250',
      doubanTvWeeklyChinese: 'Douban Chinese TV Weekly',
      doubanTvWeeklyGlobal: 'Douban Global TV Weekly',
    },
    filterMedias: {
      title: 'Filter Media Data',
      subtitle: 'Filter media data list',
      type: 'Type',
      name: 'Name',
      year: 'Year',
      vote: 'Vote',
    },
    scrapeFile: {
      title: 'Scrape File',
      subtitle: 'Scrape media info and images',
    },
    sendEvent: {
      title: 'Send Event',
      subtitle: 'Send task execution event',
    },
    fetchDownloads: {
      title: 'Fetch Downloads',
      subtitle: 'Fetch download queue task status',
      loop: 'Loop Execution',
      loopInterval: 'Loop Interval (seconds)',
    },
    fetchRss: {
      title: 'Fetch RSS Resources',
      subtitle: 'Subscribe RSS feed to get resources',
      url: 'RSS URL',
      userAgent: 'User-Agent',
      timeout: 'Timeout',
      matchMedia: 'Match Media Info',
      useProxy: 'Use Proxy',
    },
    fetchTorrents: {
      title: 'Search Site Resources',
      subtitle: 'Search site torrent resource list',
      searchType: 'Search Type',
      searchOptions: {
        name: 'Name',
        mediaList: 'Media List',
      },
      name: 'Name',
      year: 'Year',
      type: 'Type',
      season: 'Season',
      sites: 'Sites',
      matchMedia: 'Match Media Info',
    },
    sendMessage: {
      title: 'Send Message',
      subtitle: 'Send task execution message',
      channel: 'Message Channel',
      userId: 'User ID',
    },
    transferFile: {
      title: 'Organize Files',
      subtitle: 'Organize and rename files in queue',
      source: 'Source',
      sourceOptions: {
        fileList: 'File List',
        downloads: 'Downloads',
      },
    },
    filterTorrents: {
      title: 'Filter Resources',
      subtitle: 'Filter resource list',
      quality: 'Quality',
      resolution: 'Resolution',
      effect: 'Effect',
      size: 'Size Range',
      include: 'Include (Keywords, Regex)',
      exclude: 'Exclude (Keywords, Regex)',
      ruleGroups: 'Filter Rule Groups',
    },
    invokePlugin: {
      title: 'Invoke Plugin',
      subtitle: 'Call plugin to perform specific actions',
      plugin: 'Plugin',
      actionid: 'Action ID',
      actionParams: 'Action Parameters',
      loadPluginSettingFailed: 'Failed to load plugin settings',
    },
    note: {
      title: 'Note',
      subtitle: 'Add workflow description notes',
      content: 'Note Content',
      placeholder: 'Please enter note content...',
    },
    title: 'Workflow',
    share: 'Workflow Share',
    searchShares: 'Search Workflow Shares',
    noShareData: 'No shared workflows',
    sharer: 'Sharer',
    trigger: 'Trigger',
    timer: 'Timer',
    manualTrigger: 'Manual Trigger',
    actionCount: 'Action Count',
    normalFork: 'Fork Workflow',
    cancelShare: 'Cancel Share',
    cancelSuccess: 'Share cancelled successfully',
    cancelFailed: 'Failed to cancel share: {message}',
    usageCount: 'Used {count} times',
    addSuccess: 'Forked {name} successfully!',
    addFailed: 'Failed to fork {name}: {message}',
    noWorkflow: 'No Workflow',
    noWorkflowDescription: 'Click the add button to create a workflow task.',
  },
  dashboard: {
    storage: 'Storage',
    mediaStatistic: 'Media Statistics',
    weeklyOverview: 'Recent Imports',
    realTimeSpeed: 'Real-time Speed',
    scheduler: 'Background Tasks',
    cpu: 'CPU',
    memory: 'Memory',
    network: 'Network Traffic',
    upload: 'Upload',
    download: 'Download',
    library: 'My Media Library',
    playing: 'Continue Watching',
    latest: 'Recently Added',
    settings: 'Dashboard Settings',
    chooseContent: 'Choose content to display',
    adaptiveHeight: 'Adaptive Component Height',
    current: 'Current',
    episodes: 'Episodes',
    users: 'Users',
    noSchedulers: 'No Background Services',
    weeklyOverviewDescription: 'Added {count} media in the last week',
    speed: {
      totalUpload: 'Total Upload',
      totalDownload: 'Total Download',
      freeSpace: 'Free Disk Space',
    },
    processes: {
      title: 'System Processes',
      pid: 'Process ID',
      name: 'Process Name',
      runtime: 'Runtime',
      memory: 'Memory Usage',
    },
    errors: {
      loadMediaServer: 'Failed to load media server settings:',
      loadLatest: 'Failed to load recently added from media server "{server}":',
    },
  },
  media: {
    status: {
      inLibrary: 'In Library',
      missing: 'Missing',
      partiallyMissing: 'Partially Missing',
      subscribed: 'Subscribed',
    },
    minutes: 'minutes',
    overview: 'Overview',
    seasons: 'Seasons',
    seasonNumber: 'Season {number}',
    episodeCount: '{count} Episodes',
    actions: {
      searchResource: 'Search Resource',
      subscribe: 'Subscribe',
      playOnline: 'Play Online',
      playInApp: 'Play in App',
      playInWeb: 'Play in Web',
    },
    search: {
      byTitle: 'Title',
      byImdb: 'IMDB Link',
    },
    info: {
      originalTitle: 'Original Title',
      status: 'Status',
      releaseDate: 'Release Date',
      originalLanguage: 'Original Language',
      productionCountries: 'Production Countries',
      productionCompanies: 'Production Companies',
      doubanId: 'Douban ID',
    },
    subscribe: {
      normal: 'Subscribe',
      bestVersion: 'Best Version Subscribe',
      addFailed: 'Failed to add subscription: {reason}!',
      canceled: 'Subscription canceled!',
      cancelFailed: 'Failed to cancel subscription: {reason}!',
    },
    castAndCrew: 'Cast & Crew',
    recommendations: 'Recommendations',
    similar: 'Similar',
    error: {
      title: 'Error!',
      noMediaInfo: 'No media information recognized.',
    },
    server: {
      plex: 'Plex',
      jellyfin: 'Jellyfin',
      emby: 'Emby',
      appLaunchFailed: 'App launch failed, redirecting to web version',
      appNotInstalled: 'App not detected, redirecting to web version',
      downloadApp: 'Download App',
    },
  },
  subscribe: {
    normalSub: 'Subscribe',
    versionSub: 'Version Upgrade Subscribe',
    addSuccess: 'Added {name} successfully!',
    addFailed: 'Failed to add {name}: {message}!',
    cancelSuccess: 'Subscription cancelled!',
    cancelFailed: 'Failed to cancel subscription: {message}!',
    filterSubscriptions: 'Filter Subscriptions',
    name: 'Name',
    searchShares: 'Search Subscription Shares',
    keyword: 'Keyword',
    noShareData: 'No shared subscription data received, data sharing not enabled or server cannot connect.',
    noPopularData: 'No popular subscription data received, data sharing not enabled or server cannot connect.',
    noFilterData: 'No related content found with current filters, please change filter conditions.',
    noSubscribeData: 'Please search to add movie or TV show subscriptions.',
    sharer: 'Sharer',
    follow: 'Follow',
    unfollow: 'Unfollow',
    recognitionWords: 'Recognition Words',
    cancelShare: 'Cancel Share',
    usageCount: '{count} Uses',
    confirmToggle: 'Are you sure you want to {action} subscription {name}?',
    toggleSuccess: '{name} has been {action}d!',
    toggleFailed: '{action} failed: {message}',
    resetConfirm:
      'After reset, {name} will be restored to its initial state, downloaded records will be cleared, and unimported content will be downloaded again. Are you sure?',
    resetSuccess: '{name} has been reset successfully!',
    resetFailed: '{name} reset failed: {message}',
    shareStatistics: 'Share Statistics',
    shareCount: 'Shares',
    totalReuseCount: 'Total Reuse Count',
    ranking: 'Ranking',
    noStatisticsData: 'No share statistics data available',
    bestVersion: 'Version Upgrading',
    completed: 'Completed',
    subscribing: 'Subscribing',
    notStarted: 'Not Started',
    pending: 'Pending',
    paused: 'Paused',
  },
  recommend: {
    all: 'All',
    categoryMovie: 'Movies',
    categoryTV: 'TV Shows',
    categoryAnime: 'Anime',
    categoryRankings: 'Rankings',
    trendingNow: 'Trending Now',
    nowShowing: 'Now Showing',
    bangumiDaily: 'Bangumi Daily Release',
    tmdbHotMovies: 'TMDB Hot Movies',
    tmdbHotTVShows: 'TMDB Hot TV Shows',
    doubanHotMovies: 'Douban Hot Movies',
    doubanHotTVShows: 'Douban Hot TV Shows',
    doubanHotAnime: 'Douban Hot Anime',
    doubanNewMovies: 'Douban New Movies',
    doubanNewTVShows: 'Douban New TV Shows',
    doubanTop250: 'Douban Top 250 Movies',
    doubanChineseTVRankings: 'Douban Chinese TV Rankings',
    doubanGlobalTVRankings: 'Douban Global TV Rankings',
    noCategoryContent: 'No content to display in current category',
    configureContent: 'Configure Display Content',
    customizeContent: 'Customize Content',
    selectContentToDisplay: 'Select content you want to display on the page',
    selectAll: 'Select All',
    selectNone: 'Select None',
  },
  discover: {
    setTabOrder: 'Set Tab Order',
    dragToReorder: 'Drag to reorder tabs',
  },
  downloading: {
    noDownloader: 'No Downloader',
    configureDownloader: 'Please configure and enable a downloader in settings first.',
    title: 'Downloading',
    noTask: 'No Task',
    noTaskDescription: 'Downloading tasks will be displayed here.',
  },
  resource: {
    searchResults: 'Resource Search Results',
    keyword: 'Keyword',
    title: 'Title',
    year: 'Year',
    season: 'Season',
    switchingView: 'Switching View',
    backToHome: 'Back to Home',
    searching: 'Searching, please wait...',
    noData: 'No Data',
    noResourceFound: 'No resources found',
  },
  browse: {
    actor: 'Actor',
  },
  appcenter: {
    others: 'Others',
  },
  notFound: {
    title: '⚠️ Page Not Found',
    description: 'The page you tried to access does not exist. Please check if the address is correct.',
    backButton: 'Go Back',
  },
  torrent: {
    selectAll: 'Select All',
    clear: 'Clear',
    clearFilters: 'Clear Filters',
    confirm: 'Confirm',
    resources: 'resources',
    noResults: 'No results found',
    sortDefault: 'Default',
    sortSite: 'Site',
    sortSize: 'Size',
    sortSeeder: 'Seeder',
    sortPublishTime: 'Publish Time',
    filterSite: 'Site',
    filterSeason: 'Season',
    filterFreeState: 'Free State',
    filterVideoCode: 'Video Code',
    filterEdition: 'Edition',
    filterResolution: 'Resolution',
    filterReleaseGroup: 'Release Group',
    noMatchingResults: 'No matching data',
    allFilters: 'All Filters',
    clearAll: 'Clear All',
  },
  calendar: {
    episode: 'Episode {number}',
  },
  storage: {
    name: 'Name',
    type: 'Type',
    customTypeHint: 'Custom storage type, used for plugins and other scenarios',
    usedPercent: '{percent}% Used',
    noConfigNeeded: 'This storage type does not require configuration, please configure the directory directly!',
    notConfigured: 'Not Configured',
    local: 'Local',
    alipan: 'Aliyun Drive',
    u115: '115 Cloud',
    rclone: 'RClone',
    alist: 'OpenList',
    smb: 'SMB Network Share',
    custom: 'Custom',
  },
  filterRules: {
    specSub: 'Special Subtitle',
    cnSub: 'Chinese Subtitle',
    cnVoi: 'Chinese Dubbing',
    gz: 'Official Seed',
    notCnVoi: 'Exclude: Chinese Dubbing',
    hkVoi: 'Cantonese Dubbing',
    notHkVoi: 'Exclude: Cantonese Dubbing',
    free: 'Promotion: Free',
    resolution4k: 'Resolution: 4K',
    resolution1080p: 'Resolution: 1080P',
    resolution720p: 'Resolution: 720P',
    not720p: 'Exclude: 720P',
    qualityBlu: 'Quality: Blu-ray',
    notBlu: 'Exclude: Blu-ray',
    qualityBluray: 'Quality: BLURAY',
    notBluray: 'Exclude: BLURAY',
    qualityUhd: 'Quality: UHD',
    notUhd: 'Exclude: UHD',
    qualityRemux: 'Quality: REMUX',
    notRemux: 'Exclude: REMUX',
    qualityWebdl: 'Quality: WEB-DL',
    notWebdl: 'Exclude: WEB-DL',
    quality60fps: 'Quality: 60fps',
    not60fps: 'Exclude: 60fps',
    codecH265: 'Codec: H265',
    notH265: 'Exclude: H265',
    codecH264: 'Codec: H264',
    notH264: 'Exclude: H264',
    effectDolby: 'Effect: Dolby Vision',
    notDolby: 'Exclude: Dolby Vision',
    effectAtmos: 'Effect: Dolby Atmos',
    notAtmos: 'Exclude: Dolby Atmos',
    effectHdr: 'Effect: HDR',
    notHdr: 'Exclude: HDR',
    effectSdr: 'Effect: SDR',
    notSdr: 'Exclude: SDR',
    effect3d: 'Effect: 3D',
    not3d: 'Exclude: 3D',
  },
  transferType: {
    copy: 'Copy',
    move: 'Move',
    link: 'Hard Link',
    softlink: 'Soft Link',
  },
  site: {
    noSites: 'No Sites',
    noFilterData: 'No matching sites found',
    sitesWillBeShownHere: 'Added and supported sites will be displayed here.',
    title: 'Site',
    status: {
      enabled: 'Enabled',
      disabled: 'Disabled',
    },
    fields: {
      url: 'Site URL',
      priority: 'Priority',
      status: 'Status',
      rss: 'RSS URL',
      timeout: 'Timeout (seconds)',
      downloader: 'Downloader',
      cookie: 'Site Cookie',
      userAgent: 'User-Agent',
      authorization: 'Authorization Header',
      apiKey: 'API Key',
      limitAccess: 'Limit Site Access Frequency',
      limitInterval: 'Interval Period (seconds)',
      limitCount: 'Access Count per Period',
      limitSeconds: 'Access Interval (seconds)',
      useProxy: 'Use Proxy',
      browserSimulation: 'Browser Simulation',
    },
    hints: {
      url: 'Format: http://www.example.com/',
      priority: 'Lower priority value means higher priority',
      status: 'Enable/Disable site',
      rss: 'Subscription link used when subscription mode is `Site RSS`, manual input if not auto-retrieved',
      timeout: 'Site request timeout, no limit when set to 0',
      downloader: 'Downloader used for this site',
      cookie: 'Cookie information in site request header',
      userAgent: 'User-Agent of the browser used to get Cookie',
      authorization: 'Authorization information in site request header, required for special sites',
      apiKey: 'Site API Key, required for special sites',
      limitInterval: 'Duration of the rate limit control period',
      limitCount: 'Allowed access count within the period',
      limitSeconds: 'Minimum interval between each access',
      useProxy: 'Use proxy server to access this site',
      browserSimulation: 'Use browser simulation for authentic site access',
    },
    actions: {
      add: 'Add Site',
      edit: 'Edit Site',
    },
    messages: {
      addSuccess: 'Site added successfully',
      addFailed: 'Failed to add site',
      updateSuccess: 'Updated successfully',
      updateFailed: 'Update failed',
    },
    errors: {
      loadDownloader: 'Failed to load downloader settings',
    },
    testConnectivity: 'Test Connectivity',
    testing: 'Testing ...',
    testSuccess: '{name} connectivity test successful, ready to use!',
    testFailed: '{name} connectivity test failed: {message}',
    connectionNormal: 'Connection Normal',
    connectionSlow: 'Connection Slow',
    connectionFailed: 'Connection Failed',
    connectionUnknown: 'Connection Unknown',
    deleteConfirm: 'Are you sure you want to delete this site?',
    deleteSuccess: '{name} deleted successfully!',
    deleteFailed: '{name} deletion failed: {message}',
    browseResources: 'Browse Resources',
    deleteSite: 'Delete Site',
    updateCookie: 'Update Cookie',
    viewUserData: 'View User Data',
    statistics: 'Statistics',
    totalSites: 'Total Sites',
    normalSites: 'Normal Sites',
    slowSites: 'Slow Sites',
    failedSites: 'Failed Sites',
    averageTime: 'Average Time',
    successRate: 'Success Rate',
    successCount: 'Success Count',
    failCount: 'Fail Count',
    lastAccess: 'Last Access',
    timeRecords: 'Time Records',
    recentTimeRecords: 'Recent Time Records',
    accessTime: 'Access Time',
    responseTime: 'Response Time',
    noTimeRecords: 'No Time Records',
  },
  message: {
    loadMore: 'Load More',
    noMoreData: 'No more data',
  },
  logging: {
    level: 'Level',
    time: 'Time',
    program: 'Program',
    content: 'Content',
    refreshing: 'Refreshing',
  },
  moduleTest: {
    normal: 'Normal',
    disabled: 'Disabled',
    error: 'Error',
    checking: 'Checking...',
    complete: 'Check Complete',
    preparing: 'Preparing...',
    totalModules: 'Total Modules',
    recheck: 'Recheck',
  },
  nameTest: {
    recognize: 'Recognize',
    recognizing: 'Recognizing...',
    recognizeAgain: 'Recognize Again',
    title: 'Title',
    subtitle: 'Subtitle',
  },
  netTest: {
    notTested: 'Not Tested',
    testing: 'Testing...',
    normal: 'Normal',
  },
  ruleTest: {
    test: 'Test',
    testing: 'Testing...',
    testAgain: 'Test Again',
    title: 'Title',
    subtitle: 'Subtitle',
    ruleGroup: 'Rule Group',
    priority: 'Priority: {value}',
    noPriorityRule: 'No priority rule matched!',
  },
  setting: {
    about: {
      title: 'About MoviePilot',
      softwareVersion: 'Software Version',
      frontendVersion: 'Frontend Version',
      authVersion: 'Auth Resource Version',
      indexerVersion: 'Indexer Resource Version',
      configDir: 'Config Directory',
      dataDir: 'Data Directory',
      timezone: 'Timezone',
      latest: 'Latest',
      supportingSites: 'Supporting Sites',
      support: 'Support',
      documentation: 'Documentation',
      feedback: 'Feedback',
      channel: 'Release Channel',
      versions: 'Software Versions',
      latestVersion: 'Latest Version',
      currentVersion: 'Current Version',
      viewChangelog: 'View Changelog',
      changelog: 'Changelog',
      dataDirectory: '/moviepilot',
      expand: 'Expand',
      collapse: 'Collapse',
    },
    system: {
      custom: 'Custom',
      basicSettings: 'Basic Settings',
      basicSettingsDesc: 'Configure server global functions.',
      appDomain: 'Access Domain',
      appDomainHint: 'Used to add quick jump links when sending notifications',
      wallpaper: 'Background Wallpaper',
      wallpaperHint: 'Choose the source of the login page background',
      recognizeSource: 'Recognition Data Source',
      recognizeSourceHint: 'Set the default media info recognition data source',
      mediaServerSyncInterval: 'Media Server Sync Interval',
      mediaServerSyncIntervalHint: 'Time interval for syncing media server data to local',
      hours: 'hours',
      required: 'Required field, please fill in',
      numbersOnly: 'Only numbers are supported, please do not enter other characters',
      minInterval: 'Interval cannot be less than 1 hour',
      apiToken: 'API Token',
      apiTokenHint: 'Set the token value used when external requests access MoviePilot API',
      apiTokenMinChars: 'Cannot be less than 16 characters',
      apiTokenRequired: 'Required field; please enter API Token',
      apiTokenLength: 'API Token must be at least 16 characters',
      githubToken: 'Github Token',
      githubTokenFormat: 'ghp_**** or github_pat_****',
      githubTokenHint: 'Used to increase the rate limit threshold when plugins access Github API',
      ocrHost: 'OCR Server',
      ocrHostHint: 'Used for site check-in, updating site cookies and other captcha recognition',
      advancedSettings: 'Advanced Settings',
      advancedSettingsDesc: 'System advanced settings, only need to be adjusted in special cases',
      downloaders: 'Downloaders',
      downloadersDesc: 'Only the default downloader will be used by default.',
      mediaServers: 'Media Servers',
      mediaServersDesc: 'All enabled media servers will be used.',
      trimeMedia: 'TrimeMedia',
      system: 'System',
      media: 'Media',
      network: 'Network',
      log: 'Log',
      lab: 'Lab',
      downloaderSaveSuccess: 'Downloader settings saved successfully',
      downloaderSaveFailed: 'Failed to save downloader settings!',
      defaultDownloaderNotice: 'No default downloader set, [{name}] has been set as the default downloader',
      mediaServerSaveSuccess: 'Media server settings saved successfully',
      mediaServerSaveFailed: 'Failed to save media server settings!',
      saveFailed: 'Failed to save settings: {message}!',
      basicSaveSuccess: 'Basic settings saved successfully',
      advancedSaveSuccess: 'Advanced settings saved successfully',
      copySuccess: 'Copied to clipboard!',
      copyFailed: 'Copy failed: browser may not support or user blocked!',
      copyError: 'Copy failed!',
      reloading: 'Applying configuration...',
      qbittorrent: 'Qbittorrent',
      transmission: 'Transmission',
      emby: 'Emby',
      jellyfin: 'Jellyfin',
      plex: 'Plex',
      reloadSuccess: 'System configuration has taken effect',
      reloadFailed: 'Failed to reload system!',
      auxAuthEnable: 'User Auxiliary Authentication',
      auxAuthEnableHint: 'Allow external services to authenticate login and automatically create users',
      globalImageCache: 'Global Image Cache',
      globalImageCacheHint: 'Cache media images locally to improve image loading speed',
      subscribeStatisticShare: 'Share Subscription Data',
      subscribeStatisticShareHint:
        'Share subscription statistics to popular subscriptions for other MP users to reference',
      pluginStatisticShare: 'Report Plugin Installation Data',
      pluginStatisticShareHint: 'Report plugin installation data to the server for statistics and display purposes',
      workflowStatisticShare: 'Share Workflow Data',
      workflowStatisticShareHint: 'Share workflow statistics to popular workflows for other MP users to reference',
      bigMemoryMode: 'Large Memory Mode',
      bigMemoryModeHint: 'Use more memory to cache data and improve system performance',
      dbWalEnable: 'WAL Mode',
      dbWalEnableHint:
        'Can improve read/write concurrency performance, but may increase the risk of data loss in exceptional cases, requires restart to take effect',
      tmdbApiDomain: 'TMDB API Service Address',
      tmdbApiDomainPlaceholder: 'api.themoviedb.org',
      tmdbApiDomainHint: 'Customize themoviedb API domain or proxy address',
      tmdbApiDomainRequired: 'Please enter TMDB API domain',
      tmdbImageDomain: 'TMDB Image Service Address',
      tmdbImageDomainPlaceholder: 'image.tmdb.org',
      tmdbImageDomainHint: 'Customize themoviedb image service domain or proxy address',
      tmdbImageDomainRequired: 'Please enter image service domain',
      tmdbLocale: 'TMDB Metadata Language',
      tmdbLocalePlaceholder: 'en',
      tmdbLocaleHint: 'Customize themoviedb metadata language',
      metaCacheExpire: 'Media Metadata Cache Expiration Time',
      metaCacheExpireHint: 'Recognition metadata local cache time, use built-in default value when set to 0',
      metaCacheExpireRequired: 'Please enter metadata cache time',
      metaCacheExpireMin: 'Metadata cache time must be greater than or equal to 0',
      scrapFollowTmdb: 'Follow TMDB Recognition',
      scrapFollowTmdbHint:
        'When turned off, organization history will be used (if available) to avoid TMDB data changes during subscription',
      scrapOriginalImage: 'Scrap TheMovieDb Original Language Image',
      scrapOriginalImageHint: 'Scrap original language image from themoviedb, otherwise scrap metadata language image',
      fanartEnable: 'Fanart Image Data Source',
      fanartEnableHint: 'Use image data from fanart.tv',
      fanartLang: 'Fanart Language',
      fanartLangHint: 'Set language preference for Fanart images, ordered by priority when multiple selected',
      githubProxy: 'Github Acceleration Proxy',
      githubProxyPlaceholder: 'Leave empty for no proxy',
      githubProxyHint: 'Use proxy to accelerate Github access speed',
      pipProxy: 'PIP Acceleration Proxy',
      pipProxyPlaceholder: 'Leave empty for no proxy',
      pipProxyHint: 'Use proxy to accelerate pip library installation speed for plugins, etc.',
      dohEnable: 'DNS Over HTTPS',
      dohEnableHint: 'Use DOH to resolve specific domains to prevent DNS pollution',
      dohResolvers: 'DOH Servers',
      dohResolversPlaceholder: 'https://dns.google/dns-query,*******',
      dohResolversHint: 'DNS resolver server addresses, multiple addresses separated by commas',
      dohDomains: 'DOH Domains',
      dohDomainsPlaceholder: 'example.com,example2.com',
      dohDomainsHint: 'Domains to be resolved using DOH, multiple domains separated by commas',
      debug: 'Debug Mode',
      debugHint: 'When debug mode is enabled, logs will be recorded at DEBUG level to help troubleshoot issues',
      logLevel: 'Log Level',
      logLevelHint: 'Set the level of log recording to control log output volume',
      logMaxFileSize: 'Maximum Log File Size (MB)',
      logMaxFileSizeHint: 'Limit the maximum size of a single log file, logs will be split automatically when exceeded',
      logMaxFileSizeRequired: 'Maximum log file size',
      logMaxFileSizeMin: 'Maximum log file size must be greater than or equal to 1',
      logBackupCount: 'Maximum Number of Log File Backups',
      logBackupCountHint:
        'Set the maximum number of backups for each module log file, old logs will be overwritten when exceeded',
      logBackupCountRequired: 'Please enter the maximum number of log file backups',
      logBackupCountMin: 'Maximum number of log file backups must be greater than or equal to 1',
      logFileFormat: 'Log File Format',
      logFileFormatHint: 'Set the output format of log files to customize the displayed content of logs',
      pluginAutoReload: 'Plugin Hot Reload',
      pluginAutoReloadHint: 'Automatically reload after modifying plugin files, used when developing plugins',
      encodingDetectionPerformanceMode: 'Encoding Detection Performance Mode',
      encodingDetectionPerformanceModeHint:
        'Prioritize detection efficiency, but may reduce encoding detection accuracy',
      tokenizedSearch: 'Tokenized Search',
      tokenizedSearchHint:
        'Improve organization history search precision, but may increase performance overhead and unexpected results',
      tmdbLanguage: {
        zhCN: 'Simplified Chinese',
        zhTW: 'Traditional Chinese',
        en: 'English',
      },
      fanartLanguage: {
        zh: 'Chinese',
        en: 'English',
        ja: 'Japanese',
        ko: 'Korean',
        de: 'German',
        fr: 'French',
        es: 'Spanish',
        it: 'Italian',
        pt: 'Portuguese',
        ru: 'Russian',
      },
      logLevelItems: {
        debug: 'DEBUG',
        info: 'INFO',
        warning: 'WARNING',
        error: 'ERROR',
        critical: 'CRITICAL',
      },
      wallpaperItems: {
        tmdb: 'TMDB Movie Posters',
        bing: 'Bing Daily Wallpaper',
        mediaserver: 'Media Server',
        none: 'No Wallpaper',
        customize: 'Customize',
      },
      mb: 'MB',
      hour: 'hour',
      customizeWallpaperApi: 'Customize Wallpaper Api',
      customizeWallpaperApiHint:
        'It will get the image file extension format images that are allowed in settings in the content returned by the API.',
      customizeWallpaperApiRequired: 'Required field; please enter Wallpaper API',
      securityImageDomains: 'Security Image Domains',
      securityImageDomainsHint: 'Allowed image domains whitelist for caching, used to control trusted image sources',
      noSecurityImageDomains: 'No security domains',
      securityImageDomainAdd: 'Add domain, e.g.: image.tmdb.org',
      proxyHost: 'Proxy Server',
      proxyHostHint: 'Set proxy server address, support: http(s), socks5, socks5h, etc.',
      moviePilotAutoUpdate: 'Auto Update MoviePilot',
      moviePilotAutoUpdateHint: 'Automatically update MoviePilot to the latest release version when restarting',
      autoUpdateResource: 'Auto Update Resource',
      autoUpdateResourceHint: 'Automatically detect and update site resource package when restarting',
      // Scraping Switch Settings
      scrapingSwitchSettings: 'Scraping Switch Settings',
      scrapingSwitchSettingsDesc: 'Control various media file scraping function switches',
      movie: 'Movie',
      tv: 'TV Show',
      season: 'Season',
      episode: 'Episode',
      movieNfo: 'NFO',
      seasonNfo: 'NFO',
      moviePoster: 'Poster',
      movieBackdrop: 'Backdrop',
      movieLogo: 'Logo',
      movieDisc: 'Disc',
      movieBanner: 'Banner',
      movieThumb: 'Thumb',
      tvNfo: 'NFO',
      tvPoster: 'Poster',
      tvBackdrop: 'Backdrop',
      tvBanner: 'Banner',
      tvLogo: 'Logo',
      tvThumb: 'Thumb',
      seasonPoster: 'Poster',
      seasonBanner: 'Banner',
      seasonThumb: 'Thumb',
      episodeNfo: 'NFO',
      episodeThumb: 'Thumb',
      scrapingSwitchSaveFailed: 'Scraping switch settings save failed: {message}',
      scrapingSwitchSaveError: 'Scraping switch settings save failed',
    },
    site: {
      siteSync: 'Site Synchronization',
      siteSyncDesc: 'Quickly sync site data from CookieCloud',
      enableLocalCookieCloud: 'Enable Local CookieCloud Server',
      enableLocalCookieCloudHint:
        'Use built-in CookieCloud service to sync site data, service address: http://localhost:3000/cookiecloud',
      serviceAddress: 'Service Address',
      serviceAddressPlaceholder: 'https://movie-pilot.org/cookiecloud',
      serviceAddressHint: 'Remote CookieCloud service address, format: https://movie-pilot.org/cookiecloud',
      userKey: 'User KEY',
      userKeyHint: 'User KEY generated by CookieCloud browser plugin',
      e2ePassword: 'End-to-End Encryption Password',
      e2ePasswordHint: 'End-to-end encryption password generated by CookieCloud browser plugin',
      autoSyncInterval: 'Auto Sync Interval',
      autoSyncIntervalHint:
        'Time interval for automatically syncing site cookies from CookieCloud server to MoviePilot',
      syncBlacklist: 'Sync Domain Blacklist',
      syncBlacklistPlaceholder: 'Multiple domains, separated by commas',
      syncBlacklistHint: 'CookieCloud sync domain blacklist, multiple domains separated by commas',
      userAgent: 'Browser User-Agent',
      userAgentHint: 'User-Agent of the browser with CookieCloud plugin',
      siteDataRefresh: 'Site Data Refresh',
      siteDataRefreshInterval: 'Site Data Refresh Interval',
      siteDataRefreshIntervalHint: 'Time interval for refreshing site user upload/download data',
      readSiteMessage: 'Read Site Messages',
      readSiteMessageHint: 'Read site messages and send notifications when refreshing data',
      siteReset: 'Site Reset',
      confirmReset: 'Confirm to delete all site data and resync.',
      confirmResetHint:
        'Delete all site data and resync from CookieCloud. Please clear all related site settings before this operation.',
      resetSites: 'Reset Site Data',
      resettingSites: 'Resetting...',
      syncInterval: {
        hourly: 'Hourly',
        every6Hours: 'Every 6 Hours',
        every12Hours: 'Every 12 Hours',
        daily: 'Daily',
        weekly: 'Weekly',
        monthly: 'Monthly',
        never: 'Never',
      },
      saveSuccess: 'Site settings saved successfully',
      saveFailed: 'Failed to save site settings!',
      resetSuccess: 'Sites reset successfully, please wait for CookieCloud sync to complete!',
      resetFailed: 'Failed to reset sites!',
    },
    notification: {
      channels: 'Notification Channels',
      channelsDesc: 'Set message sending channel parameters',
      organizeSuccess: 'Media Import',
      downloadAdded: 'Download Added',
      subscribeAdded: 'Subscribe Added',
      subscribeComplete: 'Subscribe Complete',
      templateConfigTitle: 'Message Template',
      templateConfigDesc: 'Set message template, support Jinja2 syntax.',
      templateSaveFailed: 'Failed to save template!',
      templateSaveSuccess: 'Template saved successfully',
      templateLoadFailed: 'Failed to load template!',
      scope: 'Notification Scope',
      scopeDesc: 'Corresponding message types will only be sent to specified users.',
      messageType: 'Message Type',
      scopeRange: 'Scope',
      operationUserOnly: 'Operation User Only',
      adminOnly: 'Admin Only',
      userAndAdmin: 'Operation User and Admin',
      allUsers: 'All Users',
      sendTime: 'Notification Send Time',
      sendTimeDesc: 'Set the time range for sending messages.',
      startTime: 'Start Time',
      endTime: 'End Time',
      saveSuccess: 'Notification settings saved successfully',
      saveFailed: 'Failed to save notification settings!',
      switchSaveSuccess: 'Message type switches saved successfully',
      switchSaveFailed: 'Failed to save message type switches!',
      timeSaveSuccess: 'Notification send time saved successfully',
      timeSaveFailed: 'Failed to save notification send time!',
      channel: 'Notification',
      wechat: 'WeChat',
      resourceDownload: 'Resource Download',
      mediaImport: 'Media Import',
      subscription: 'Subscription',
      site: 'Site',
      mediaServer: 'Media Server',
      manualProcess: 'Manual Process',
      plugin: 'Plugin',
      other: 'Other',
      telegram: 'Telegram',
      slack: 'Slack',
      synologyChat: 'SynologyChat',
      voceChat: 'VoceChat',
      webPush: 'WebPush',
      custom: 'Custom Notification',
    },
    words: {
      customIdentifiers: 'Custom Identifiers',
      identifiersDesc: 'Add rules to preprocess torrent names or file names to correct identification',
      identifiersPlaceholder: 'Support regular expressions, special characters need \\ escape, one line for each rule',
      identifiersHint: 'Support regular expressions, special characters need \\ escape, one line for each rule',
      formatTitle: 'Supported configuration formats (mind the spaces):',
      formatContent:
        'Block words\n' +
        'Word to replace => Replacement\n' +
        'Front word <> Back word >> Episode offset (EP)\n' +
        'Word to replace => Replacement && Front word <> Back word >> Episode offset (EP)\n' +
        'Replacement format supports: &#123;[tmdbid/doubanid=xxx;type=movie/tv;s=xxx;e=xxx]&#125; to directly specify TMDBID/Douban ID, where s and e are season and episode numbers (optional)',
      identifierSaveSuccess: 'Custom identifiers saved successfully',
      identifierSaveFailed: 'Failed to save custom identifiers!',

      customReleaseGroups: 'Custom Release/Subtitle Groups',
      releaseGroupsDesc: 'Add release/subtitle groups that cannot be identified.',
      releaseGroupsPlaceholder:
        'Support regular expressions, special characters need \\ escape, one line for each group',
      releaseGroupsHint: 'Support regular expressions, special characters need \\ escape, one line for each group',
      releaseGroupSaveSuccess: 'Custom release/subtitle groups saved successfully',
      releaseGroupSaveFailed: 'Failed to save custom release/subtitle groups!',

      customization: 'Custom Placeholders',
      customizationDesc: 'Add custom placeholder regex patterns, use {customization} in rename format.',
      customizationPlaceholder:
        'Support regular expressions, special characters need \\ escape, separate multiple matches with new lines',
      customizationHint:
        'Support regular expressions, special characters need \\ escape, separate multiple matches with new lines',
      customizationSaveSuccess: 'Custom placeholders saved successfully',
      customizationSaveFailed: 'Failed to save custom placeholders!',

      transferExcludeWords: 'File Organization Block Words',
      excludeWordsDesc: 'Files or directories containing block words will not be organized.',
      excludeWordsPlaceholder:
        'Support regular expressions, special characters need \\ escape, one line for each block word',
      excludeWordsHint: 'Support regular expressions, special characters need \\ escape, one line for each block word',
      excludeWordsSaveSuccess: 'File organization block words saved successfully',
      excludeWordsSaveFailed: 'Failed to save file organization block words!',
    },
    search: {
      basicSettings: 'Basic Settings',
      basicSettingsDesc: 'Set data sources, rule groups and other basic information',
      recognizeSource: 'Recognition Data Source',
      recognizeSourceDesc:
        'Default is TMDB. Douban is usually more friendly for Chinese works, but some foreign works have incomplete information.',
      themoviedb: 'TheMovieDb',
      douban: 'Douban',
      filterRuleGroup: 'Filter Rule Group',
      filterRuleGroupDesc: 'Set filter rule groups used during download process.',
      downloadLabel: 'Download Task Label',
      downloadLabelDesc: 'Download labels in downloader, used for filtering queries.',
      downloadLabelHint: 'Support multiple labels, separated by commas',
      downloadSite: 'Search Sites',
      downloadSiteDesc: 'Set site scope for specific category searches.',
      movieSites: 'Movie Sites',
      tvSites: 'TV Show Sites',
      animeSites: 'Anime Sites',
      saveSites: 'Save Sites',
      saveSuccess: 'Search settings saved successfully',
      saveFailed: 'Failed to save search settings!',
      saveRuleFailed: 'Failed to save rules!',
      movieCategory: 'Movies',
      tvCategory: 'TV Shows',
      animeCategory: 'Anime',
      downloadUser: 'Remote Search Auto Download User List',
      downloadUserHint:
        'Whether to automatically download when searching with Telegram, WeChat, etc., comma separated, set to all to represent all users auto-download',
      multipleNameSearch: 'Multiple Name Resource Search',
      multipleNameSearchHint:
        'Search site resources using multiple names (Chinese, English, etc.) and merge search results, will increase site access frequency',
      downloadSubtitle: 'Download Site Subtitles',
      downloadSubtitleHint: 'Check if site resources have separate subtitle files and download them automatically',
      mediaSource: 'Media Search Data Source',
      mediaSourceHint: 'Data sources and sorting used when searching for media information',
      filterRuleGroupHint: 'Filter results by selected filter rule groups when searching for media information',
      downloadUserPlaceholder: 'UserID1,UserID2',
      downloadLabelPlaceholder: 'MOVIEPILOT',
    },
    directory: {
      storage: 'Storage',
      storageDesc: 'Set up local or cloud storage.',
      directory: 'Directory',
      directoryDesc: 'Set up media file organization directory structure, matching in sequence.',
      organizeAndScrap: 'Organization & Scraping',
      organizeAndScrapDesc: 'Set rename format, scraping options, etc.',
      scrapSource: 'Scraping Data Source',
      scrapSourceHint: 'Metadata source for scraping',
      movieRenameFormat: 'Movie Rename Format',
      movieRenameFormatHint:
        'Using Jinja2 syntax, format reference: https://jinja.palletsprojects.com/en/3.0.x/templates',
      tvRenameFormat: 'TV Show Rename Format',
      tvRenameFormatHint: 'Using Jinja2 syntax, format reference: https://jinja.palletsprojects.com/en/3.0.x/templates',
      saveSuccess: 'Storage settings saved successfully',
      saveFailed: 'Failed to save storage settings!',
      directorySaveSuccess: 'Directory settings saved successfully',
      directorySaveFailed: 'Failed to save directory settings!',
      organizeSaveSuccess: 'Organization options saved successfully',
      organizeSaveFailed: 'Failed to save organization options!',
      duplicateDirectoryName: 'Duplicate directory names exist! Cannot save, please modify!',
      defaultDirName: 'Directory',
      storageSaveSuccess: 'Storage settings saved successfully',
      storageSaveFailed: 'Failed to save storage settings!',
    },
    rule: {
      customRules: 'Custom Rules',
      customRulesDesc: 'Custom priority rule items',
      priorityRuleGroups: 'Priority Rule Groups',
      priorityRuleGroupsDesc: 'Preset priority rule groups for use in search and subscription.',
      downloadRules: 'Download Rules',
      downloadRulesDesc: 'Choose the best option when multiple resources are matched.',
      resourcePriority: 'Resource Priority',
      sitePriority: 'Site Priority',
      siteUpload: 'Site Upload',
      resourceSeeder: 'Resource Seeders',
      emptyIdError: 'A rule has an empty ID, cannot save. Please modify!',
      emptyNameError: 'A rule has an empty name, cannot save. Please modify!',
      duplicateIdError: 'Duplicate rule IDs exist! Cannot save, please modify!',
      duplicateNameError: 'Duplicate rule names exist! Cannot save, please modify!',
      customRuleSaveSuccess: 'Custom rules saved successfully',
      customRuleSaveFailed: 'Failed to save custom rules!',
      emptyGroupNameError: 'A rule group has an empty name! Cannot save, please modify!',
      duplicateGroupNameError: 'Duplicate rule group names exist! Cannot save, please modify!',
      ruleGroupSaveSuccess: 'Priority rule groups saved successfully',
      ruleGroupSaveFailed: 'Failed to save priority rule groups!',
      customRuleCopySuccess: 'Custom rules copied to clipboard!',
      customRuleCopyFailed: 'Failed to copy custom rules: browser may not support or user blocked!',
      customRuleCopyError: 'Failed to copy custom rules!',
      ruleGroupCopySuccess: 'Priority rule groups copied to clipboard!',
      ruleGroupCopyFailed: 'Failed to copy priority rule groups: browser may not support or user blocked!',
      ruleGroupCopyError: 'Failed to copy priority rule groups!',
      currentPriorityRules: 'Current Download Priority Rules',
      currentPriorityRulesHint: 'Higher priority for items at the front, unselected items are not included in sorting',
      importCustomRules: 'Import Custom Rules',
      importRuleGroups: 'Import Priority Rule Groups',
      importFailed: 'Failed to import rules! Cannot parse input data!',
      importUnknownType: 'Failed to import rules! Unknown data type!',
      duplicateValue: 'Duplicate values exist',
      importNoId: 'Import failed! Found rules without IDs, may belong to priority rule groups!',
      importHasId: 'Import failed! Found rules with IDs, may belong to custom rules!',
    },
    scheduler: {
      title: 'Scheduled Jobs',
      subtitle: 'Includes built-in system services and plugin services',
      provider: 'Provider',
      taskName: 'Task Name',
      taskStatus: 'Task Status',
      nextRunTime: 'Next Run Time',
      execute: 'Execute',
      noService: 'No background services',
      running: 'Running',
      stopped: 'Stopped',
      waiting: 'Waiting',
      executeSuccess: 'Scheduled job execution request submitted successfully!',
    },
    subscribe: {
      basicSettings: 'Basic Settings',
      basicSettingsDesc: 'Set subscription mode, cycle and other basic settings',
      subscribeSites: 'Subscribe Sites',
      subscribeSitesDesc: 'Only selected sites will be used in subscriptions.',
      mode: 'Subscription Mode',
      modeHint: 'Auto: automatically crawl site homepage, Site RSS: subscribe via site RSS link',
      rssInterval: 'Site RSS Interval',
      rssIntervalHint: 'Set the site RSS running cycle, effective when subscription mode is `Site RSS`',
      filterRuleGroup: 'Subscription Priority Rule Group',
      filterRuleGroupHint: 'Filter subscriptions based on selected filter rule groups',
      bestVersionRuleGroup: 'Version Upgrade Priority Rule Group',
      bestVersionRuleGroupHint: 'Filter version upgrade subscriptions based on selected filter rule groups',
      timedSearch: 'Subscription Scheduled Search',
      timedSearchHint: 'Search all sites every 24 hours to supplement resources that may be missed by subscription',
      checkLocalMedia: 'Check File System Resources',
      checkLocalMediaHint:
        'Scan the storage directory for existing resource files to avoid duplicate downloads; regardless of whether it is enabled, the media server will be checked',
      modes: {
        auto: 'Auto',
        rss: 'Site RSS',
      },
      intervals: {
        min5: '5 minutes',
        min10: '10 minutes',
        min20: '20 minutes',
        min30: '30 minutes',
        hour1: '1 hour',
        hour12: '12 hours',
        day1: '1 day',
      },
      saveSuccess: 'Subscription sites saved successfully',
      saveFailed: 'Failed to save subscription sites!',
      settingsSaveSuccess: 'Subscription basic settings saved successfully',
      settingsSaveFailed: 'Failed to save subscription basic settings!',
    },
    cache: {
      title: 'Cache Management',
      subtitle: 'Manage torrent cache data',
      filterByTitle: 'Filter by Title',
      filterBySite: 'Filter by Site',
      selectSite: 'Select Site',
      refresh: 'Refresh Cache',
      deleteSelected: 'Delete Selected',
      clearAll: 'Clear All Cache',
      refreshSuccess: 'Cache refresh completed',
      refreshFailed: 'Failed to refresh cache',
      clearSuccess: 'Cache clear completed',
      clearFailed: 'Failed to clear cache',
      deleteSuccess: 'Cache item deleted successfully',
      deleteFailed: 'Failed to delete cache item',
      deleteSelectedSuccess: 'Successfully deleted {count} cache items',
      deleteSelectedFailed: 'Failed to delete cache items',
      loadFailed: 'Failed to load cache data',
      selectDeleteWarning: 'Please select cache items to delete',
      reidentify: 'Re-identify',
      reidentifySuccess: 'Re-identification completed',
      reidentifyFailed: 'Re-identification failed',
      poster: 'Poster',
      torrentTitle: 'Title',
      site: 'Site',
      size: 'Size',
      publishTime: 'Publish Time',
      recognitionResult: 'Recognition Result',
      actions: 'Actions',
      unrecognized: 'Unrecognized',
      noData: 'No cache data',
      noDataHint: 'Click "Refresh Cache" button to get the latest torrent cache',
      reidentifyDialog: {
        title: 'Re-identify',
        torrentInfo: 'Torrent Info',
        tmdbId: 'TMDB ID',
        tmdbIdHint: 'Optional, manually specify TMDB ID for recognition',
        doubanId: 'Douban ID',
        doubanIdHint: 'Optional, manually specify Douban ID for recognition',
        autoHint: 'If no ID is specified, the torrent will be automatically re-identified',
        cancel: 'Cancel',
        confirm: 'Re-identify',
      },
      mediaType: {
        movie: 'Movie',
        tv: 'TV Show',
      },
      clearConfirm: 'Are you sure you want to clear all cache?',
    },
  },
  dialog: {
    progress: {
      processing: 'Processing',
    },
    subscribeSeason: {
      title: 'Subscribe - {title}',
      selectGroup: 'Select Episode Group',
      defaultGroup: 'Default',
      seasonCount: '{count} Seasons',
      episodeCount: '{count} Episodes',
      seasonNumber: 'Season {number}',
      airDate: 'First aired on {date}',
      voteAverage: '{score}',
      status: {
        exists: 'Exists',
        partial: 'Partially Missing',
        missing: 'Missing',
      },
      submit: 'Submit Subscription',
      selectSeasons: 'Please select seasons to subscribe',
    },
    userAddEdit: {
      add: 'Add User',
      edit: 'Edit User',
      username: 'Username',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      email: 'Email',
      nickname: 'Nickname',
      status: 'Status',
      active: 'Active',
      inactive: 'Inactive',
      superUser: 'Super User',
      otp: 'Enable Two-Factor Authentication',
      avatar: 'Avatar',
      uploadAvatar: 'Upload Avatar',
      resetDefaultAvatar: 'Reset Default Avatar',
      restoreCurrentAvatar: 'Restore Current Avatar',
      notifications: 'Notifications',
      wechat: 'WeChat UserID',
      telegram: 'Telegram UserID',
      slack: 'Slack UserID',
      vocechat: 'VoceChat UserID',
      synologyChat: 'SynologyChat UserID',
      webPush: 'WebPush',
      creatingUser: 'Creating user [{name}], please wait',
      updatingUser: 'Updating user [{name}], please wait',
      usernameRequired: 'Username cannot be empty',
      usernameExists: 'Username already exists',
      passwordMismatch: 'The two passwords do not match',
      userCreated: 'User [{name}] created successfully',
      userCreateFailed: 'Failed to create user: {message}',
      userUpdateSuccess: 'User [{name}] updated successfully',
      userUpdateFailed: 'Failed to update user: {message}',
      userDeleteSuccess: 'User [{name}] deleted successfully',
      userDeleteFailed: 'Failed to delete user: {message}',
      invalidFile: 'The uploaded file does not meet the requirements, please choose a new avatar',
      fileSizeLimit: 'File size must not exceed 800KB',
      avatarUploadSuccess: 'New avatar uploaded successfully, will take effect after saving!',
      resetAvatarSuccess: 'Reset to default avatar, will take effect after saving!',
      restoreAvatarSuccess: 'Restored current avatar!',
      deleteConfirm: 'Confirm delete user [{name}]?',
      saveUserInfo: 'Save User Information',
      cannotDeleteCurrentUser: 'Cannot delete current logged-in user',
      deleteUser: 'Delete User',
      permissions: {
        title: 'Permission Settings',
        presetNormal: 'Normal User',
        presetAdmin: 'Administrator',
        discovery: 'Discovery',
        discoveryDesc: 'Access recommendation and exploration features',
        search: 'Search',
        searchDesc: 'Search site resources and add downloads',
        subscribe: 'Subscribe',
        subscribeDesc: 'Manage movie and TV show subscriptions',
        manage: 'Manage',
        manageDesc: 'Access download management and site management etc.',
      },
    },
    searchBar: {
      search: 'Search',
      searchPlaceholder: 'Search features, subscriptions, settings...',
      recentSearches: 'Recent Searches',
      noRecentSearches: 'No recent search history',
      functions: 'Functions',
      noFunctionsFound: 'No matching functions',
      plugins: 'Plugins',
      noPluginsFound: 'No matching plugins',
      subscriptions: 'Subscriptions',
      noSubscriptionsFound: 'No matching subscriptions',
      searchSites: 'Search Sites',
      selectSites: 'Select Sites',
      collections: 'Collections',
      collectionSearch: 'Related series works',
      actorSearch: 'Related actors, directors, etc.',
      historySearch: 'Related history records',
      subscribeShareSearch: 'Related subscription shares',
      siteResources: 'Site Resources',
      searchInSites: 'Search for torrent resources in sites',
      relatedResources: 'Related Resources',
      searchTip: 'You can search for movies, TV shows, actors, resources, etc.',
    },
    searchSite: {
      selectSites: 'Select Sites',
      siteSearch: 'Site Search',
      searchAllSites: 'Selected {selected}/{total} sites',
      selectAll: 'Select All',
      deselectAll: 'Deselect All',
      confirm: 'Confirm',
      cancel: 'Cancel',
    },
    importCode: {
      import: 'Import',
      title: 'Import Code',
    },
    addDownload: {
      confirmDownload: 'Confirm Download',
      downloader: 'Downloader (Default)',
      saveDirectory: 'Save Directory (Auto)',
      defaultPlaceholder: 'Leave empty for default',
      autoPlaceholder: 'Leave empty for auto-match',
      downloading: 'Downloading...',
      startDownload: 'Start Download',
      downloadSuccess: '{site} {title} downloaded successfully!',
      downloadFailed: '{site} {title} download failed: {message}!',
    },
    subscribeShare: {
      shareSubscription: 'Share Subscription',
      season: 'Season {number}',
      title: 'Title',
      description: 'Description',
      descriptionHint:
        'Add a description about this subscription. Search terms, recognition words, etc. will be included in the share by default',
      shareUser: 'Share User',
      shareUserHint: "Sharer's nickname",
      confirmShare: 'Confirm Share',
      shareSuccess: '{name} shared successfully!',
      shareFailed: '{name} share failed: {message}!',
    },
    workflowShare: {
      shareWorkflow: 'Share Workflow',
      title: 'Title',
      description: 'Description',
      descriptionHint:
        'Add a description about this workflow. Actions and flows will be included in the share by default',
      shareUser: 'Share User',
      shareUserHint: "Sharer's nickname",
      confirmShare: 'Confirm Share',
      shareSuccess: '{name} shared successfully!',
      shareFailed: '{name} share failed: {message}!',
      securityWarning: 'Security Warning',
      securityWarningMessage:
        'Before sharing, please ensure the workflow does not contain sensitive information such as PassKey in RSS links to avoid information leakage.',
    },
    u115Auth: {
      loginTitle: '115 Cloud Login',
      scanQrCode: 'Please scan with WeChat or 115 client',
      scanned: 'Scanned, please confirm login',
      complete: 'Complete',
      reset: 'Reset',
    },
    aliyunAuth: {
      loginTitle: 'Aliyun Drive Login',
      scanQrCode: 'Please scan with Aliyun Drive App',
      scanned: 'Scanned',
      complete: 'Complete',
      reset: 'Reset',
    },
    rcloneConfig: {
      title: 'RClone Configuration',
      filePath: 'rclone config file path',
      fileContent: 'rclone config file content',
      defaultContent:
        '# Please fill in your rclone config file content here \n# Please refer to https://rclone.org/docs/ \n# Storage node name must be: MP',
      complete: 'Complete',
      reset: 'Reset',
    },
    alistConfig: {
      title: 'OpenList Configuration',
      serverUrl: 'OpenList server address',
      username: 'Username',
      password: 'Password',
      tokenUrl: 'Token acquisition address',
      loginType: 'Login method',
      loginTypeOptions: {
        guest: 'Guest',
        username: 'Username & Password',
        token: 'Token',
      },
      complete: 'Complete',
      reset: 'Reset',
    },
    smbConfig: {
      title: 'SMB Network Share Configuration',
      host: 'SMB Server Address',
      hostHint: 'IP address or hostname of the SMB server',
      share: 'Share Name',
      shareHint: 'Name of the shared folder to connect to',
      username: 'Username',
      usernameHint: 'SMB login username',
      password: 'Password',
      passwordHint: 'SMB login password',
      domain: 'Domain',
      domainHint: 'SMB domain name, such as WORKGROUP or domain controller name',
      complete: 'Complete',
      reset: 'Reset',
    },
    workflowAddEdit: {
      addTitle: 'Add Workflow',
      editTitle: 'Edit Workflow',
      name: 'Name',
      namePlaceholder: 'Workflow name',
      desc: 'Description',
      descPlaceholder: 'Workflow description',
      enabled: 'Enabled',
      triggerType: 'Trigger Type',
      triggerTypeTimer: 'Timer Trigger',
      triggerTypeEvent: 'Event Trigger',
      triggerTypeManual: 'Manual Trigger',
      schedule: 'Schedule',
      cronExpr: 'Cron Expression',
      cronExprDesc: 'Cron expression for workflow scheduling',
      eventType: 'Event Type',
      eventTypePlaceholder: 'Please select event type',
      nameRequired: 'Please fill in complete information!',
      triggerRequired: 'Please select trigger type!',
      timerRequired: 'Please fill in timer expression!',
      eventTypeRequired: 'Please select event type!',
      addSuccess: 'Task created successfully, please edit the workflow!',
      addFailed: 'Failed to create task: {message}',
      editSuccess: 'Task modified successfully!',
      editFailed: 'Failed to modify task: {message}',
      cancel: 'Cancel',
      confirm: 'Confirm',
    },
    workflowActions: {
      title: 'Edit Workflow',
      noActionsMessage: 'Workflow has no actions, please add actions',
      addAction: 'Add Action',
      editAction: 'Edit Action',
      deleteAction: 'Delete Action',
      moveUp: 'Move Up',
      moveDown: 'Move Down',
      nameLabel: 'Action Name',
      nameRequired: 'Action name cannot be empty',
      typeLabel: 'Action Type',
      typeRequired: 'Action type cannot be empty',
      paramsLabel: 'Action Parameters',
      outputLabel: 'Action Output',
      saveAction: 'Save Action',
      cancelAction: 'Cancel',
      confirmDeleteTitle: 'Confirm Delete Action',
      confirmDeleteMessage: 'Are you sure you want to delete this action? This operation cannot be undone.',
      yesDelete: 'Yes, Delete',
      noCancel: 'Cancel',
      invalidConnection: 'Invalid connection: cannot connect to self or ports of the same type!',
      componentNotFound: 'Component {component} not found',
      componentAdded: 'Component added to canvas',
      saveSuccess: 'Task workflow saved successfully!',
      saveFailed: 'Failed to save task workflow: {message}',
      importTitle: 'Import Task Workflow',
      importSuccess: 'Import successful!',
      importFailed: 'Import failed!',
      codeCopied: 'Task workflow code copied to clipboard!',
    },
    siteCookieUpdate: {
      title: 'Update Site Cookie & UA',
      processing: 'Please wait...',
      updating: 'Updating {site} Cookie & UA...',
      success: '{site} Cookie & UA updated successfully!',
      failed: '{site} update failed: {message}',
      updateButton: 'Start Update',
    },
    siteAddEdit: {
      addTitle: 'Add Site',
      editTitle: 'Edit Site',
      nameLabel: 'Site Name',
      urlLabel: 'Site URL',
      iconLabel: 'Site Icon',
      uploadIcon: 'Upload Icon',
      cookie: 'Cookie',
      rssUrl: 'RSS Link',
      enableLabel: 'Enable',
      pubEnableLabel: 'Public Resources',
      priorityLabel: 'Priority',
      signInLabel: 'Sign In',
      proxies: 'Proxies',
      userInfo: 'User Info',
      cancel: 'Cancel',
      confirm: 'Save',
    },
    pluginConfig: {
      title: 'Plugin Configuration',
      save: 'Save',
      close: 'Close',
      viewData: 'View Data',
      saving: 'Saving {name} configuration...',
      saveSuccess: 'Plugin {name} configuration saved',
      saveFailed: 'Failed to save plugin {name} configuration: {message}',
    },
    pluginData: {
      title: 'Plugin Data',
      save: 'Save',
      close: 'Close',
    },
    pluginMarketSetting: {
      title: 'Plugin Market Settings',
      repoUrl: 'Plugin Repository URL',
      repoPlaceholder: 'Format: https://github.com/jxxghp/MoviePilot-Plugins/,https://github.com/xxxx/xxxxxx/',
      repoHint: 'Multiple URLs separated by lines, only Github repositories are supported',
      close: 'Close',
      save: 'Save',
      saveSuccess: 'Plugin repository saved successfully',
      saveFailed: 'Failed to save plugin repository: {message}!',
    },
    userAuth: {
      title: 'User Authentication',
      codeLabel: 'Authentication Code',
      codePlaceholder: 'Please enter authentication code',
      authBtn: 'Start Authentication',
      closeBtn: 'Close',
      selectSite: 'Select Authentication Site',
      selectSiteRequired: 'Please select authentication site!',
      siteConfigNotExist: 'Site configuration does not exist!',
      fieldRequired: 'Please enter {name}!',
      authSuccess: 'User authentication successful, please log in again!',
      authFailed: 'Authentication failed: {message}',
    },
    transferQueue: {
      title: 'Organization Queue',
      name: 'Name',
      type: 'Type',
      state: 'Status',
      progress: 'Progress',
      startTime: 'Start Time',
      speedTitle: 'Speed',
      pathTitle: 'Path',
      sizeTitle: 'Size',
      waitingState: 'Waiting',
      runningState: 'Organizing',
      finishedState: 'Completed',
      failedState: 'Failed',
      cancelledState: 'Cancelled',
      noTasks: 'No tasks being organized',
      processing: 'Please wait ...',
      stopAll: 'Stop All',
      startAll: 'Start All',
      refresh: 'Refresh',
      close: 'Close',
    },
    reorganize: {
      title: 'Organize',
      sourceTitle: 'Source File',
      targetTitle: 'Target File',
      processingTitle: 'Processing',
      confirmTitle: 'Confirm',
      selectFile: 'Select File',
      selectTarget: 'Select Target',
      selectMediaType: 'Select Media Type',
      movie: 'Movie',
      tv: 'TV Show',
      selectTmdbId: 'Select TMDB ID',
      selectMediaInfo: 'Select Media Info',
      selectTargetPath: 'Select Target Path',
      selectTargetDir: 'Select Target Directory',
      selectFileName: 'Select Filename',
      confirmMoving: 'Please confirm move!',
      sourceLabel: 'Source file:',
      targetLabel: 'Target directory:',
      filenameLabel: 'Filename:',
      close: 'Close',
      next: 'Next',
      previous: 'Previous',
      confirm: 'Confirm',
      manualTitle: 'Manual Organization',
      multipleItemsTitle: '{count} Items',
      singleItemTitle: '{path}',
      targetStorage: 'Target Storage',
      targetStorageHint: 'Organization target storage',
      transferType: 'Organization Method',
      transferTypeHint: 'File operation organization method',
      targetPath: 'Target Path',
      targetPathHint: 'Organization target path, leave empty for auto-match',
      targetPathPlaceholder: 'Leave empty for auto',
      mediaType: 'Type',
      mediaTypeHint: 'File media type',
      tmdbId: 'TheMovieDb ID',
      doubanId: 'Douban ID',
      mediaIdHint: 'Query media ID by name, leave empty for auto recognition',
      mediaIdPlaceholder: 'Leave empty for auto recognition',
      episodeGroup: 'Episode Group ID',
      episodeGroupHint: 'Specify episode group',
      episodeGroupPlaceholder: 'Manually query episode group',
      season: 'Season',
      seasonHint: 'Which season',
      episodeDetail: 'Episode',
      episodeDetailHint: 'Episode number or range, e.g. 1 or 1,2',
      episodeDetailPlaceholder: 'Start episode,End episode',
      episodeFormat: 'Episode Positioning',
      episodeFormatHint: 'Use {ep} to position episode number part in filename to assist recognition',
      episodeFormatPlaceholder: 'Use {ep} to position episode',
      episodeOffset: 'Episode Offset',
      episodeOffsetHint: 'Episode offset calculation, e.g. -10 or EP*2',
      episodeOffsetPlaceholder: 'e.g. -10',
      episodePart: 'Specify Part',
      episodePartHint: 'Specify part, e.g. part1',
      episodePartPlaceholder: 'e.g. part1',
      minFileSize: 'Min File Size (MB)',
      minFileSizeHint: 'Only organize files larger than minimum file size',
      typeFolderOption: 'Classify by Type',
      typeFolderHint: 'Add subdirectory by media type in target path during organization',
      categoryFolderOption: 'Classify by Category',
      categoryFolderHint: 'Add subdirectory by media category in target path during organization',
      scrapeOption: 'Scrape Metadata',
      scrapeHint: 'Automatically scrape metadata after organization',
      fromHistoryOption: 'Reuse Historical Recognition Info',
      fromHistoryHint: 'Use media info already recognized in historical organization records',
      addToQueue: 'Add to Organization Queue',
      reorganizeNow: 'Organize Now',
      auto: 'Auto',
      processing: 'Processing ...',
      successMessage: 'File {name} has been added to the organization queue!',
    },
    subscribeEdit: {
      titleDefault: 'Default Subscription Rules',
      titleEdit: 'Edit Subscription',
      seasonFormat: 'Season {number}',
      tabs: {
        basic: 'Basic',
        advance: 'Advanced',
      },
      searchKeyword: 'Search Keywords',
      searchKeywordHint: 'Specify keywords used when searching sites',
      totalEpisode: 'Total Episodes',
      totalEpisodeHint: 'Total number of episodes',
      startEpisode: 'Start Episode',
      startEpisodeHint: 'Starting episode number to subscribe',
      quality: 'Quality',
      qualityHint: 'Subscription resource quality',
      resolution: 'Resolution',
      resolutionHint: 'Subscription resource resolution',
      effect: 'Effects',
      effectHint: 'Subscription resource effects',
      subscribeSites: 'Subscription Sites',
      subscribeSitesHint: 'Range of sites for subscription, use system settings if none selected',
      downloader: 'Downloader',
      downloaderHint: 'Specify downloader for this subscription',
      savePath: 'Save Path',
      savePathHint: 'Specify download save path for this subscription, leave empty to use default download directory',
      bestVersion: 'Version Upgrade',
      bestVersionHint: 'Perform version upgrade subscription based on upgrade priorities',
      searchImdbid: 'Search Using ImdbID',
      searchImdbidHint: 'Use ImdbID for precise resource searching',
      showEditDialog: 'Edit More Rules When Subscribing',
      showEditDialogHint: 'Show this edit subscription dialog when adding subscription',
      include: 'Include (Keywords, Regex)',
      includeHint: 'Include rules, supports regular expressions',
      exclude: 'Exclude (Keywords, Regex)',
      excludeHint: 'Exclude rules, supports regular expressions',
      filterGroups: 'Priority Rule Groups',
      filterGroupsHint: 'Filter subscriptions by selected filter rule groups',
      episodeGroup: 'Specify Episode Group',
      episodeGroupHint: 'Recognize and scrape by specific episode group',
      season: 'Specify Season',
      seasonHint: 'Specify any season for subscription',
      mediaCategory: 'Custom Category',
      mediaCategoryHint: 'Specify category name, leave empty for auto-recognition',
      customWords: 'Custom Recognition Words',
      customWordsHint: 'Recognition words only used for this subscription',
      customWordsPlaceholder:
        'Block word\nReplaced word => Replacement word\nPrefix <> Suffix >> Episode offset (EP)\nReplaced word => Replacement word && Prefix <> Suffix >> Episode offset (EP)\nReplacement word supports format: &#123; tmdbid/doubanid=xxx;type=movie/tv;s=xxx;e=xxx &#125; to directly specify TMDBID/Douban ID recognition, where s, e are season and episode numbers (optional)',
      cancelSubscribe: 'Cancel Subscription',
      save: 'Save',
      cancelSubscribeConfirm: 'Are you sure you want to cancel the subscription?',
    },
    subscribeFiles: {
      title: 'Downloaded Files',
      noFilesMessage: 'No files',
      close: 'Close',
      downloadTab: 'Download Files',
      libraryTab: 'Media Library Files',
      episodeColumn: 'Episode',
      torrentColumn: 'Torrent',
      fileColumn: 'File',
      itemsPerPage: 'Items per page',
      pageText: '{0}-{1} of {2} items',
      loadingText: 'Loading...',
      noData: 'No data',
      season: 'Season {number}',
    },
    subscribeHistory: {
      title: '{type} Subscription History',
      resubscribe: 'Resubscribe',
      resubscribeMovie: 'Resubscribing {name}...',
      resubscribeTv: 'Resubscribing {name} Season {season}...',
      season: 'Season {season}',
      noData: 'No completed subscriptions',
    },
    siteUserData: {
      title: 'Site User Data',
      updateTime: 'Update Time',
      username: 'Username',
      uploadTitle: 'Upload',
      uploadTotal: 'Total Upload',
      downloadTitle: 'Download',
      downloadTotal: 'Total Download',
      seedingTitle: 'Seeding',
      seedingCount: 'Total Seeding Count',
      seedingSize: 'Total Seeding Size',
      userLevel: 'User Level',
      msgCount: 'Unread Messages',
      inviteCount: 'Invites',
      bonus: 'Bonus Points',
      ratio: 'Ratio',
      joinTime: 'Join Time',
      trafficHistory: 'Traffic History',
      seedingDistribution: 'Seeding Distribution',
      volumeTitle: 'Volume',
      countTitle: 'Count:',
      noData: 'None',
      refreshing: 'Refreshing site data...',
      close: 'Close',
    },
    siteResource: {
      browseTitle: 'Browse - {name}',
      searchKeyword: 'Search Keyword',
      resourceCategory: 'Resource Category',
      search: 'Search',
      itemsPerPage: 'Items Per Page',
      noData: 'No Data',
      loading: 'Loading...',
      titleColumn: 'Title',
      timeColumn: 'Time',
      sizeColumn: 'Size',
      seedersColumn: 'Seeders',
      peersColumn: 'Peers',
      viewDetails: 'View Details',
      downloadTorrent: 'Download Torrent',
      pageText: '{0}-{1} of {2} items',
    },
    forkSubscribe: {
      title: 'Copy Subscription',
      selectSubscriber: 'Select Copy Target',
      overwriteExisting: 'Overwrite Existing Subscription',
      overwriteExistingHint: 'Whether to overwrite when target user already has this subscription',
      confirm: 'Confirm',
      cancel: 'Cancel',
    },
  },
  file: {
    newFolder: 'New Folder',
    autoRecognizeName: 'Auto Recognize Name',
    createFolder: 'Create Folder',
    fileName: 'File Name',
    fileSize: 'File Size',
    fileType: 'File Type',
    lastModified: 'Last Modified',
    actions: 'Actions',
    rename: 'Rename',
    delete: 'Delete',
    confirmFileDelete: 'Confirm Delete',
    upload: 'Upload',
    download: 'Download',
    preview: 'Preview',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    moveUp: 'Go Back',
    sortByName: 'Sort by Name',
    sortByTime: 'Sort by Time',
    currentName: 'Current Name',
    newName: 'New Name',
    includeSubfolders: 'Auto rename all media files in directory',
    emptyFolder: 'Empty Folder',
    noFilesInFolder: 'No files in this folder',
    autoRecognize: 'Auto Recognize Name',
    directoryTree: 'Directory Tree',
    rootDirectory: 'Root Directory',
    noDirectories: 'No directories available',
    directory: 'Directory',
    file: 'File',
    size: 'Size',
    modifyTime: 'Modify Time',
    noFiles: 'No directories or files',
    emptyDirectory: 'Empty directory',
    confirmDelete: 'Are you sure you want to delete {type} {name}?',
    confirmBatchDelete: 'Are you sure you want to delete {count} selected items?',
    deleting: 'Deleting {name}...',
    recognize: 'Recognize',
    recognizing: 'Recognizing {path}...',
    recognizeFailed: '{path} recognition failed!',
    scrape: 'Scrape',
    scraping: 'Scraping {path}...',
    scrapeCompleted: '{path} scraping completed!',
    confirmScrape: 'Are you sure you want to scrape {path}?',
    confirmBatchScrape: 'Are you sure you want to scrape {count} selected items?',
    renaming: 'Renaming {name}...',
    renamingAll: 'Renaming {path} and all files in the directory...',
    close: 'Close',
    loadingDirectoryStructure: 'Loading directory structure...',
    reorganize: 'Reorganize',
  },
  person: {
    alias: 'Also Known As:',
    credits: 'Credits',
    biography: 'Biography',
    birthday: 'Birthday',
    placeOfBirth: 'Place of Birth',
  },
  error: {
    title: 'Error!',
    networkError: 'Unable to get media information, please check your network connection.',
    serverError: 'Server error, please try again later.',
    notFound: 'Requested resource not found.',
  },
  plugin: {
    sort: {
      popular: 'Popular',
      name: 'Plugin Name',
      author: 'Author',
      repository: 'Plugin Repository',
      latest: 'Latest Release',
    },
    installingPlugin: 'Installing plugin...',
    installing: 'Installing {name} v{version} ...',
    installSuccess: 'Plugin {name} installed successfully!',
    installFailed: 'Plugin {name} installation failed: {message}',
    filterPlugins: 'Filter Plugins',
    name: 'Name',
    hasNewVersion: 'Has New Version',
    running: 'Running',
    author: 'Author',
    label: 'Label',
    repository: 'Repository',
    sortTitle: 'Sort',
    filter: 'Filter: {name}',
    noMatchingContent: 'No matching content found',
    pleaseInstallFromMarket: 'Please install plugins from the plugin market',
    allPluginsInstalled: 'All plugins are installed',
    searchPlugins: 'Search Plugins',
    searchPlaceholder: 'Search by plugin name or description',
    uninstalling: 'Uninstalling {name} ...',
    uninstallSuccess: 'Plugin {name} uninstalled successfully!',
    uninstallFailed: 'Plugin {name} uninstallation failed: {message}',
    updating: 'Updating {name} ...',
    updateSuccess: 'Plugin {name} updated successfully!',
    updateFailed: 'Plugin {name} update failed: {message}',
    noPlugins: 'No plugins installed',
    installed: 'Installed',
    notInstalled: 'Not Installed',
    hasUpdate: 'Update Available',
    configuring: 'Configuring',
    enable: 'Enable',
    disable: 'Disable',
    settings: 'Settings',
    projectHome: 'Project Home',
    updateHistory: 'Update History',
    installToLocal: 'Install to Local',
    totalDownloads: 'Total {count} downloads',
    viewData: 'View Data',
    update: 'Update',
    reset: 'Reset',
    uninstall: 'Uninstall',
    viewLogs: 'View Logs',
    authorHome: 'Author Home',
    confirmUninstall: 'Are you sure you want to uninstall plugin {name}?',
    confirmReset:
      'This operation will restore plugin {name} to default settings and clear all related data. Are you sure you want to continue?',
    resetSuccess: 'Plugin {name} data has been reset',
    resetFailed: 'Plugin {name} reset failed: {message}',
    updateHistoryTitle: '{name} Update History',
    updateToLatest: 'Update to Latest Version',
    updatingTo: 'Updating {name} to v{version} ...',
    folderNameEmpty: 'Folder name cannot be empty',
    folderExists: 'Folder already exists',
    folderCreateSuccess: 'Folder created successfully',
    folderRenameSuccess: 'Folder renamed successfully',
    folderRenameFailed: 'Failed to rename folder',
    folderDeleteSuccess: 'Folder deleted successfully',
    folderDeleteFailed: 'Failed to delete folder',
    removeFromFolderSuccess: 'Plugin removed from folder',
    operationFailed: 'Operation failed',
    saveFolderConfigFailed: 'Failed to save folder config',
    newFolder: 'New Folder',
    folderName: 'Folder Name',
    cancel: 'Cancel',
    create: 'Create',
    clone: 'Clone',
    cloneTitle: 'Create Plugin Clone',
    cloneSubtitle: 'Create an independent clone instance for {name}',
    cloneFeature: 'Plugin Clone Feature',
    cloneDescription:
      'Create an independent copy of the plugin with separate configuration and data, suitable for multi-account, testing environments, etc.',
    suffix: 'Clone Suffix',
    suffixPlaceholder: 'e.g.: Test, Backup, Site1',
    suffixHint: 'Unique identifier to distinguish clones, only letters and numbers allowed',
    suffixRequired: 'Clone suffix cannot be empty',
    suffixFormatError: 'Only letters and numbers allowed',
    suffixLengthError: 'Length cannot exceed 20 characters',
    cloneName: 'Clone Name',
    cloneNamePlaceholder: 'e.g.: Auto Backup Test Version',
    cloneNameHint: 'Display name for the clone plugin (optional)',
    cloneDefaultName: '{name} Clone',
    cloneDescriptionLabel: 'Clone Description',
    cloneDescriptionPlaceholder: 'Describe the purpose and features of this clone...',
    cloneDescriptionHint: 'Detailed description of the clone plugin purpose (optional)',
    cloneDefaultDescription: '{description} (Clone Version)',
    cloneVersion: 'Version',
    cloneVersionPlaceholder: 'e.g.: 1.0, 2.1.0',
    cloneVersionHint: 'Custom version number for the clone plugin (optional)',
    cloneIcon: 'Icon URL',
    cloneIconPlaceholder: 'https://example.com/icon.png',
    cloneIconHint: 'Custom icon for the clone plugin (optional)',
    cloneNotice:
      'Clone plugins are disabled by default after creation and need to be manually configured and enabled. The clone suffix cannot be modified once set.',
    createClone: 'Create Clone',
    cloning: 'Creating clone for {name}...',
    cloneSuccess: 'Plugin clone {name} created successfully!',
    cloneFailed: 'Plugin clone creation failed: {message}',
    cloneFailedGeneral: 'Plugin clone creation failed',
    logTitle: 'Plugin Logging',
    quickAccess: 'Quick Access',
    noPluginsWithPage: 'No plugins with detail pages available',
    tapToOpen: 'Tap to Return',
    recentlyUsed: 'Recently Used',
    allPlugins: 'All Plugins',
    noRecentPlugins: 'None',
  },
  profile: {
    personalInfo: 'Personal Information',
    uploadNewAvatar: 'Upload New Avatar',
    avatarFormatError: 'The uploaded file does not meet requirements, please select a new avatar',
    avatarSizeError: 'File size must not exceed 800KB',
    avatarUploadSuccess: 'New avatar uploaded successfully, will take effect after saving!',
    resetAvatarSuccess: 'Reset to default avatar, will take effect after saving!',
    restoreAvatarSuccess: 'Restored current avatar!',
    savingInProgress: 'Saving in progress, please wait...',
    usernameRequired: 'Username cannot be empty',
    passwordMismatch: 'The two passwords do not match',
    usernameChangeSuccess: '[{oldName}] renamed to [{newName}], user information saved successfully!',
    saveSuccess: 'User information saved successfully!',
    saveFailedWithNameChange: '[{oldName}] renamed to [{newName}], information save failed: {message}!',
    saveFailed: 'User information save failed: {message}!',
    nickname: 'Nickname',
    nicknamePlaceholder: 'Display nickname, takes precedence over username',
    accountBinding: 'Account Binding',
    wechatUser: 'WeChat User',
    telegramUser: 'Telegram User',
    slackUser: 'Slack User',
    vocechatUser: 'VoceChat User',
    synologychatUser: 'SynologyChat User',
    doubanUser: 'Douban User',
    twoFactorAuthentication: 'Two-Factor Authentication',
    enableTwoFactor: 'Enable Two-Factor Authentication',
    disableTwoFactor: 'Disable Two-Factor Authentication',
    otpGenerateFailed: 'Failed to get OTP URI: {message}!',
    otpDisableSuccess: 'Two-factor authentication disabled successfully!',
    otpDisableFailed: 'Failed to disable OTP: {message}!',
    otpCodeRequired: 'Please enter the 6-digit verification code',
    otpEnableSuccess: 'Two-factor authentication enabled successfully!',
    otpEnableFailed: 'Failed to enable OTP: {message}!',
    authenticatorApp: 'Authenticator App',
    authenticatorAppDescription:
      'Use an authenticator app like Google Authenticator, Microsoft Authenticator, Authy, or 1Password to scan the QR code. It will generate a 6-digit code for you to enter below.',
    secretKeyTip:
      "If you're having trouble with the QR code, select manual entry in your app and enter the code above.",
    enterVerificationCode: 'Enter verification code to confirm enabling two-factor authentication',
    avatarFormatTip: 'JPG, PNG, GIF, WEBP formats allowed, maximum size 800KB.',
  },
  transferHistory: {
    title: 'Transfer History',
    searchPlaceholder: 'Search transfer records',
    titleColumn: 'Title',
    pathColumn: 'Path',
    modeColumn: 'Mode',
    sizeColumn: 'Size',
    dateColumn: 'Date',
    statusColumn: 'Status',
    actionsColumn: 'Actions',
    seasonEpisode: 'Season/Episode',
    transferQueue: 'Transfer Queue',
    groupMode: 'Group Mode',
    listMode: 'List Mode',
    deleteConfirm: 'Confirm delete {title} {seasons}{episodes}?',
    deleteConfirmBatch: 'Confirm delete {count} records?',
    deleteRecordOnly: 'Delete Record Only',
    deleteSourceOnly: 'Delete Record and Source File',
    deleteDestOnly: 'Delete Record and Media Library File',
    deleteAll: 'Delete All',
    transferMode: {
      copy: 'Copy',
      move: 'Move',
      link: 'Hard Link',
      softlink: 'Soft Link',
      rclone_copy: 'Rclone Copy',
      rclone_move: 'Rclone Move',
    },
    status: {
      success: 'Success',
      failed: 'Failed',
      unknown: 'Unknown',
    },
    noData: 'No Data',
    loading: 'Loading...',
    pageSize: 'Items Per Page',
    pageInfo: '{begin} - {end} / {total}',
    actions: {
      redo: 'Reorganize',
      delete: 'Delete',
    },
    progress: {
      processing: 'Processing',
      pleaseWait: 'Please wait...',
    },
    table: {
      emptyTitle: 'Actions',
    },
  },
  customRule: {
    error: {
      emptyIdName: 'Rule ID and rule name cannot be empty',
      idOccupied: 'Current rule ID is occupied by built-in rule',
      nameOccupied: 'Current rule name is occupied by built-in rule',
      idExists: 'Rule ID [{id}] already exists',
      nameExists: 'Rule name [{name}] already exists',
    },
    title: '{id} - Configuration',
    field: {
      ruleId: 'Rule ID',
      ruleName: 'Rule Name',
      include: 'Include',
      exclude: 'Exclude',
      sizeRange: 'Size (MB)',
      seeders: 'Seeders',
      publishTime: 'Publish Time (min)',
    },
    placeholder: {
      ruleId: 'Required; no duplicate with other rule IDs',
      ruleName: 'Required; no duplicate with other rule names',
      include: 'Keywords/Regex',
      exclude: 'Keywords/Regex',
      sizeRange: '0/1-10',
      seeders: '0/1-10',
      publishTime: '0/1-10',
    },
    hint: {
      ruleId: 'Combination of letters and numbers; no spaces',
      ruleName: 'Use alias for easy identification',
      include: 'Keywords or regex that must be included, separated by ｜',
      exclude: 'Keywords or regex that must not be included, separated by ｜',
      sizeRange: 'Minimum resource file size or range (MB)',
      seeders: 'Minimum number of seeders or range',
      publishTime: 'Minimum time since publication or range (min)',
    },
    action: {
      confirm: 'Confirm',
    },
  },
  downloader: {
    title: 'Downloader',
    name: 'Name',
    type: 'Type',
    enabled: 'Enabled',
    customTypeHint: 'Custom downloader type, for plugin scenarios',
    default: 'Default',
    host: 'Host',
    username: 'Username',
    password: 'Password',
    category: 'Auto Category Management',
    sequentail: 'Sequential Download',
    force_resume: 'Force Resume',
    first_last_piece: 'First/Last Piece Priority',
    saveSuccess: 'Downloader settings saved successfully',
    saveFailed: 'Failed to save downloader settings',
    nameRequired: 'Name cannot be empty',
    nameDuplicate: 'Name already exists',
    defaultChanged: 'Default downloader exists, has been replaced',
  },
  filterRule: {
    title: 'Filter Rule',
    groupName: 'Group Name',
    priority: 'Priority',
    rules: 'Rules',
    add: 'Add Rule',
    import: 'Import Rules',
    share: 'Share Rules',
    save: 'Save Rules',
    nameRequired: 'Rule group name cannot be empty',
    nameDuplicate: 'Rule group name already exists',
    importSuccess: 'Rules imported successfully',
    importFailed: 'Failed to import rules',
    shareSuccess: 'Rules copied to clipboard',
    shareFailed: 'Failed to copy rules',
    mediaType: 'Media Type',
    category: 'Media Category',
    mediaTypeItems: {
      movie: 'Movie',
      tv: 'TV Show',
      anime: 'Anime',
      collection: 'Collection',
      unknown: 'Unknown',
    },
  },
  mediaserver: {
    type: 'Type',
    customTypeHint: 'Custom media server type, for plugin scenarios',
    enableMediaServer: 'Enable Media Server',
    nameRequired: 'Required; cannot be duplicated',
    serverAlias: 'Media server alias',
    host: 'Host',
    hostPlaceholder: 'http(s)://ip:port',
    hostHint: 'Server address, format: http(s)://ip:port',
    playHost: 'External Playback URL',
    playHostPlaceholder: 'http(s)://domain:port',
    playHostHint: 'URL for playback page redirection, format: http(s)://domain:port',
    apiKey: 'API Key',
    embyApiKeyHint: 'API key generated in Emby Settings -> Advanced -> API Keys',
    jellyfinApiKeyHint: 'API key generated in Jellyfin Settings -> Advanced -> API Keys',
    plexToken: 'X-Plex-Token',
    plexTokenHint: 'X-Plex-Token obtained from Plex request URL in browser F12 -> Network',
    username: 'Username',
    password: 'Password',
    syncLibraries: 'Sync Libraries',
    syncLibrariesHint: 'Only selected libraries will be synchronized',
    nameExists: '【{name}】 already exists, please use a different name',
  },
  bangumi: {
    category: 'Category',
    sort: 'Sort',
    year: 'Year',
    cat: {
      other: 'Other',
      tv: 'TV',
      ova: 'OVA',
      movie: 'Movie',
      web: 'WEB',
    },
    sortType: {
      rank: 'Rank',
      date: 'Date',
    },
  },
  tmdb: {
    type: 'Type',
    sort: 'Sort',
    genre: 'Genre',
    language: 'Language',
    rating: 'Rating',
    sortType: {
      popularityDesc: 'Popularity Descending',
      popularityAsc: 'Popularity Ascending',
      releaseDateDesc: 'Release Date Descending',
      releaseDateAsc: 'Release Date Ascending',
      firstAirDateDesc: 'First Air Date Descending',
      firstAirDateAsc: 'First Air Date Ascending',
      voteAverageDesc: 'Vote Average Descending',
      voteAverageAsc: 'Vote Average Ascending',
    },
    genreType: {
      action: 'Action',
      adventure: 'Adventure',
      animation: 'Animation',
      comedy: 'Comedy',
      crime: 'Crime',
      documentary: 'Documentary',
      drama: 'Drama',
      family: 'Family',
      fantasy: 'Fantasy',
      history: 'History',
      horror: 'Horror',
      music: 'Music',
      mystery: 'Mystery',
      romance: 'Romance',
      scienceFiction: 'Science Fiction',
      tvMovie: 'TV Movie',
      thriller: 'Thriller',
      war: 'War',
      western: 'Western',
      actionAdventure: 'Action & Adventure',
      kids: 'Kids',
      news: 'News',
      reality: 'Reality',
      sciFiFantasy: 'Sci-Fi & Fantasy',
      soap: 'Soap',
      talk: 'Talk',
      warPolitics: 'War & Politics',
    },
    languageType: {
      zh: 'Chinese',
      en: 'English',
      ja: 'Japanese',
      ko: 'Korean',
      fr: 'French',
      de: 'German',
      es: 'Spanish',
      it: 'Italian',
      ru: 'Russian',
      pt: 'Portuguese',
      ar: 'Arabic',
      hi: 'Hindi',
      th: 'Thai',
    },
  },
  douban: {
    type: 'Type',
    sort: 'Sort',
    genre: 'Genre',
    zone: 'Region',
    year: 'Year',
    sortType: {
      comprehensive: 'Comprehensive',
      releaseDate: 'Release Date',
      recentHot: 'Recent Hot',
      highScore: 'High Score',
    },
    genreType: {
      comedy: 'Comedy',
      romance: 'Romance',
      action: 'Action',
      scienceFiction: 'Science Fiction',
      animation: 'Animation',
      mystery: 'Mystery',
      crime: 'Crime',
      thriller: 'Thriller',
      adventure: 'Adventure',
      music: 'Music',
      history: 'History',
      fantasy: 'Fantasy',
      horror: 'Horror',
      war: 'War',
      biography: 'Biography',
      musical: 'Musical',
      martialArts: 'Martial Arts',
      erotic: 'Erotic',
      disaster: 'Disaster',
      western: 'Western',
      documentary: 'Documentary',
      shortFilm: 'Short Film',
    },
    zoneType: {
      chinese: 'Chinese',
      europeanAmerican: 'European & American',
      korean: 'Korean',
      japanese: 'Japanese',
      mainlandChina: 'Mainland China',
      usa: 'USA',
      hongKong: 'Hong Kong',
      taiwan: 'Taiwan',
      uk: 'UK',
      france: 'France',
      germany: 'Germany',
      italy: 'Italy',
      spain: 'Spain',
      india: 'India',
      thailand: 'Thailand',
      russia: 'Russia',
      canada: 'Canada',
      australia: 'Australia',
      ireland: 'Ireland',
      sweden: 'Sweden',
      brazil: 'Brazil',
      denmark: 'Denmark',
    },
    yearType: {
      '2020s': '2020s',
      '2010s': '2010s',
      '2000s': '2000s',
      '1990s': '1990s',
      '1980s': '1980s',
      '1970s': '1970s',
      '1960s': '1960s',
    },
  },
  directory: {
    alias: 'Directory Alias',
    mediaType: 'Media Type',
    mediaCategory: 'Media Category',
    resourceStorage: 'Resource Storage',
    resourceDirectory: 'Resource Directory',
    sortByType: 'Sort by Type',
    sortByCategory: 'Sort by Category',
    autoTransfer: 'Auto Transfer',
    monitorMode: 'Monitor Mode',
    libraryStorage: 'Library Storage',
    libraryDirectory: 'Library Directory',
    transferType: 'Transfer Type',
    overwriteMode: 'Overwrite Mode',
    smartRename: 'Smart Rename',
    scrapingMetadata: 'Scrape Metadata',
    sendNotification: 'Send Notification',
    noTransfer: 'No Transfer',
    downloaderMonitor: 'Downloader Monitor',
    directoryMonitor: 'Directory Monitor',
    manualTransfer: 'Manual Transfer',
    performanceMode: 'Performance Mode',
    compatibilityMode: 'Compatibility Mode',
    pleaseSelectStorage: 'Please select storage',
    pleaseSelectLibraryStorage: 'Please select library storage',
    pleaseSelectDownloadStorage: 'Please select download storage',
    noSupportedTransferType: 'No supported transfer type',
    never: 'Never',
    always: 'Always',
    byFileSize: 'By File Size',
    keepLatestOnly: 'Keep Latest Only',
  },
  validators: {
    required: 'This field is required',
    number: 'Please enter a number',
  },
  folder: {
    settingAppearance: 'Appearance Settings',
    rename: 'Rename',
    deleteFolder: 'Delete Folder',
    folderNameCannotBeEmpty: 'Folder name cannot be empty',
    confirmDeleteFolder:
      'Are you sure you want to delete folder "{folderName}"? Plugins in this folder will be moved back to the main list.',
    folderSettingsSaved: 'Folder settings saved',
    renameFolder: 'Rename Folder',
    folderName: 'Folder Name',
    folderAppearanceSettings: 'Folder Appearance Settings',
    showFolderIcon: 'Show Folder Icon',
    icon: 'Icon',
    iconColor: 'Icon Color',
    backgroundGradient: 'Background Gradient',
    customBackgroundImageURL: 'Custom Background Image URL (Optional)',
    customBackgroundImageHint: 'Supports web image URLs, leave blank for gradient background',
    pluginCount: '{count} Plugins',
  },
}
