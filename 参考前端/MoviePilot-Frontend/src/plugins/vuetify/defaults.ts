export default {
  IconBtn: {
    icon: true,
    color: 'default',
    variant: 'text',
    VIcon: {
      size: 24,
    },
  },
  VAlert: {
    VBtn: {
      color: undefined,
    },
  },
  VAvatar: {
    // ℹ️ Remove after next release
    variant: 'flat',
    VIcon: {
      size: 24,
    },
  },
  VBadge: {
    // set v-badge default color to primary
    color: 'primary',
  },
  VBtn: {
    // set v-btn default color to primary
    color: 'primary',
    elevation: 0,
  },
  VCard: {
    elevation: 0,
    rounded: 'lg',
  },
  VMenu: {
    elevation: 0,
  },
  VChip: {
    elevation: 0,
  },
  VBottomSheet: {
    elevation: 0,
  },
  VDialog: {
    elevation: 0,
    rounded: 'lg',
  },
  VExpansionPanels: {
    elevation: 0,
  },
  VList: {
    color: 'primary',
    elevation: 0,
  },
  VListItem: {
    rounded: 'md',
  },
  VPagination: {
    activeColor: 'primary',
  },
  VTabs: {
    // set v-tabs default color to primary
    color: 'primary',
    VSlideGroup: {
      showArrows: true,
    },
  },
  VTooltip: {
    // set v-tooltip default location to top
    location: 'top',
  },
  VCheckboxBtn: {
    color: 'primary',
    hideDetails: 'auto',
  },
  VCheckbox: {
    // set v-checkbox default color to primary
    color: 'primary',
    hideDetails: 'auto',
  },
  VRadioGroup: {
    color: 'primary',
    hideDetails: 'auto',
  },
  VRadio: {
    color: 'primary',
    hideDetails: 'auto',
  },
  VSelect: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
    menuProps: { elevation: 0 },
  },
  VRangeSlider: {
    // set v-range-slider default color to primary
    color: 'primary',
    density: 'comfortable',
    thumbLabel: true,
    hideDetails: 'auto',
  },
  VRating: {
    // set v-rating default color to primary
    color: 'rgba(var(--v-theme-on-background),0.23)',
    activeColor: 'warning',
    halfIncrements: true,
  },
  VProgressCircular: {
    // set v-progress-circular default color to primary
    color: 'primary',
  },
  VSlider: {
    // set v-slider default color to primary
    color: 'primary',
    hideDetails: 'auto',
  },
  VTextField: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
  },
  VAutocomplete: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
  },
  VCombobox: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
    menuProps: { elevation: 0 },
  },
  VFileInput: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
  },
  VTextarea: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
  },
  VSwitch: {
    // set v-switch default color to primary
    color: 'primary',
    hideDetails: 'auto',
  },
}
