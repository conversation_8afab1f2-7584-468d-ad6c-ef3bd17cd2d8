import type { VuetifyOptions } from 'vuetify'

const theme: VuetifyOptions['theme'] = {
  defaultTheme: 'light',
  themes: {
    light: {
      dark: false,
      colors: {
        'primary': '#9155FD',
        'secondary': '#8A8D93',
        'on-secondary': '#FFFFFF',
        'success': '#56CA00',
        'info': '#16B1FF',
        'warning': '#FFB400',
        'error': '#FF4C51',
        'on-primary': '#FFFFFF',
        'on-success': '#FFFFFF',
        'on-warning': '#FFFFFF',
        'background': '#F4F5FA',
        'on-background': '#3A3541',
        'on-surface': '#3A3541',
        'grey-50': '#FAFAFA',
        'grey-100': '#F0F2F8',
        'grey-200': '#EEEEEE',
        'grey-300': '#E0E0E0',
        'grey-400': '#BDBDBD',
        'grey-500': '#9E9E9E',
        'grey-600': '#757575',
        'grey-700': '#616161',
        'grey-800': '#424242',
        'grey-900': '#212121',
        'perfect-scrollbar-thumb': '#DBDADE',
        'skin-bordered-background': '#FFFFFF',
        'skin-bordered-surface': '#FFFFFF',
      },

      variables: {
        'code-color': '#D400FF',
        'overlay-scrim-background': '#3A3541',
        'overlay-scrim-opacity': 0.5,
        'hover-opacity': 0.04,
        'focus-opacity': 0.1,
        'selected-opacity': 0.12,
        'activated-opacity': 0.1,
        'pressed-opacity': 0.14,
        'dragged-opacity': 0.1,
        'border-color': '#3A3541',
        'table-header-background': '#F9FAFC',
        'custom-background': '#F9F8F9',

        // Shadows
        'shadow-key-umbra-opacity': 'rgba(var(--v-theme-on-surface), 0.08)',
        'shadow-key-penumbra-opacity': 'rgba(var(--v-theme-on-surface), 0.12)',
        'shadow-key-ambient-opacity': 'rgba(var(--v-theme-on-surface), 0.04)',
      },
    },
    dark: {
      dark: true,
      colors: {
        'primary': '#6E66ED',
        'secondary': '#8A8D93',
        'on-secondary': '#FFFFFF',
        'success': '#56CA00',
        'info': '#16B1FF',
        'warning': '#FFB400',
        'error': '#FF4C51',
        'on-primary': '#FFFFFF',
        'on-success': '#FFFFFF',
        'on-warning': '#FFFFFF',
        'background': '#0E1116',
        'on-background': '#E7E3FC',
        'surface': '#14161F',
        'on-surface': '#E7E3FC',
        'grey-50': '#2A2E42',
        'grey-100': '#474360',
        'grey-200': '#4A5072',
        'grey-300': '#5E6692',
        'grey-400': '#7983BB',
        'grey-500': '#8692D0',
        'grey-600': '#AAB3DE',
        'grey-700': '#B6BEE3',
        'grey-800': '#CFD3EC',
        'grey-900': '#E7E9F6',
        'perfect-scrollbar-thumb': '#4A5072',
        'skin-bordered-background': '#312d4b',
        'skin-bordered-surface': '#312d4b',
      },
      variables: {
        'code-color': '#d400ff',
        'overlay-scrim-background': '#191D21',
        'overlay-scrim-opacity': 0.6,
        'hover-opacity': 0.04,
        'focus-opacity': 0.1,
        'selected-opacity': 0.12,
        'activated-opacity': 0.1,
        'pressed-opacity': 0.14,
        'dragged-opacity': 0.1,
        'border-color': '#E7E3FC',
        'table-header-background': '#14161F',
        'custom-background': '#373452',
        // Shadows
        'shadow-key-umbra-opacity': 'rgba(20, 18, 33, 0.08)',
        'shadow-key-penumbra-opacity': 'rgba(20, 18, 33, 0.12)',
        'shadow-key-ambient-opacity': 'rgba(20, 18, 33, 0.04)',
      },
    },
    purple: {
      dark: true,
      colors: {
        'primary': '#9155FD',
        'secondary': '#8A8D93',
        'on-secondary': '#FFFFFF',
        'success': '#56CA00',
        'info': '#16B1FF',
        'warning': '#FFB400',
        'error': '#FF4C51',
        'on-primary': '#FFFFFF',
        'on-success': '#FFFFFF',
        'on-warning': '#FFFFFF',
        'background': '#28243D',
        'on-background': '#E7E3FC',
        'surface': '#312D4B',
        'on-surface': '#E7E3FC',
        'grey-50': '#2A2E42',
        'grey-100': '#474360',
        'grey-200': '#4A5072',
        'grey-300': '#5E6692',
        'grey-400': '#7983BB',
        'grey-500': '#8692D0',
        'grey-600': '#AAB3DE',
        'grey-700': '#B6BEE3',
        'grey-800': '#CFD3EC',
        'grey-900': '#E7E9F6',
        'perfect-scrollbar-thumb': '#4A5072',
        'skin-bordered-background': '#312d4b',
        'skin-bordered-surface': '#312d4b',
      },
      variables: {
        'code-color': '#d400ff',
        'overlay-scrim-background': '#2C2942',
        'overlay-scrim-opacity': 0.6,
        'hover-opacity': 0.04,
        'focus-opacity': 0.1,
        'selected-opacity': 0.12,
        'activated-opacity': 0.1,
        'pressed-opacity': 0.14,
        'dragged-opacity': 0.1,
        'border-color': '#E7E3FC',
        'table-header-background': '#3D3759',
        'custom-background': '#373452',

        // Shadows
        'shadow-key-umbra-opacity': 'rgba(20, 18, 33, 0.08)',
        'shadow-key-penumbra-opacity': 'rgba(20, 18, 33, 0.12)',
        'shadow-key-ambient-opacity': 'rgba(20, 18, 33, 0.04)',
      },
    },
    transparent: {
      dark: true,
      colors: {
        'primary': '#A370F7',
        'secondary': '#8A8D93',
        'on-secondary': '#FFFFFF',
        'success': '#66BB6A',
        'info': '#42A5F5',
        'warning': '#FFA726',
        'error': '#EF5350',
        'on-primary': '#FFFFFF',
        'on-success': '#FFFFFF',
        'on-warning': '#FFFFFF',
        'background': '#1C1C1C',
        'on-background': '#E7E3FC',
        'surface': 'rgba(30, 30, 30, 0.3)',
        'on-surface': '#E7E3FC',
        'surface-variant': 'rgba(30, 30, 30, 0.2)',
        'on-surface-variant': 'rgba(255, 255, 255, 0.65)',
        'grey-50': 'rgba(42, 46, 66, 0.15)',
        'grey-100': 'rgba(71, 67, 96, 0.15)',
        'grey-200': 'rgba(74, 80, 114, 0.15)',
        'grey-300': 'rgba(94, 102, 146, 0.15)',
        'grey-400': 'rgba(121, 131, 187, 0.15)',
        'grey-500': 'rgba(134, 146, 208, 0.15)',
        'grey-600': 'rgba(170, 179, 222, 0.15)',
        'grey-700': 'rgba(182, 190, 227, 0.15)',
        'grey-800': 'rgba(207, 211, 236, 0.15)',
        'grey-900': 'rgba(231, 233, 246, 0.15)',
        'perfect-scrollbar-thumb': 'rgba(158, 158, 190, 0.4)',
        'skin-bordered-background': 'rgba(30, 30, 30, 0.3)',
        'skin-bordered-surface': 'rgba(30, 30, 30, 0.3)',
        'card-background': 'rgba(30, 30, 30, 0.3)',
      },
      variables: {
        'code-color': '#6D9EEB',
        'overlay-scrim-background': '0, 0, 0',
        'overlay-scrim-opacity': 0.7,
        'hover-opacity': 0.1,
        'focus-opacity': 0.15,
        'selected-opacity': 0.2,
        'activated-opacity': 0.15,
        'pressed-opacity': 0.2,
        'dragged-opacity': 0.15,
        'border-color': '#E7E3FC',
        'table-header-background': 'rgba(30, 30, 30, 0.3)',
        'custom-background': 'rgba(30, 30, 30, 0.3)',
        'card-background': 'rgba(30, 30, 30, 0.3)',

        // Shadows
        'shadow-key-umbra-opacity': 'rgba(0, 0, 0, 0.07)',
        'shadow-key-penumbra-opacity': 'rgba(0, 0, 0, 0.1)',
        'shadow-key-ambient-opacity': 'rgba(0, 0, 0, 0.05)',
      },
    },
  },
}

export default theme
