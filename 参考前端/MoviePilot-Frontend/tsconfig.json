{
  "compilerOptions": {
    "baseUrl": "./",
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "isolatedModules": true,
    "strict": true,
    "jsx": "preserve",
    "jsxFactory": "h",
    "jsxFragmentFactory": "Fragment",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@layouts/*": [
        "src/@layouts/*"
      ],
      "@layouts": [
        "src/@layouts"
      ],
      "@core/*": [
        "src/@core/*"
      ],
      "@core": [
        "src/@core"
      ],
      "@validators": [
        "src/@validators"
      ],
      "@validators/*": [
        "src/@validators/*"
      ],
      "@images/*": [
        "src/assets/images/*"
      ],
      "@styles/*": [
        "src/styles/*"
      ],
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost",
      "WebWorker"
    ],
    "skipLibCheck": true,
    "types": [
      "vite/client",
      "vite-plugin-pages/client",
      "vite-plugin-vue-layouts/client"
    ]
  },
  "include": [
    "vite.config.*",
    "env.d.ts",
    "shims.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "themeConfig.ts",
    "auto-imports.d.ts",
    "components.d.ts",
    "src/service-worker.ts",
    "public/service.js"
  ],
  "exclude": [
    "dist",
    "node_modules",
    "src/@iconify/*"
  ]
}